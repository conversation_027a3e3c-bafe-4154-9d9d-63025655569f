我想创建一个自己学校的网站，请参考以下需求进行代码编写（目前是想在本地测试，基本开发完成后再部署）：
基于用户需求及知德中学的校徽特色（紫色与白色相结合），结合搜索结果的学校网站设计原则与案例分析，以下是针对知德中学的网站设计方案：
（所有功能的代码文件分别放在各自的文件夹中）
---

### **一、设计核心理念**
1. **品牌与文化的视觉化表达**  
   - **主色调**：以校徽的紫色（象征智慧、创造力）与白色（象征纯洁、公正）为主色，搭配浅灰或低饱和度的辅助色（如浅蓝、米白）平衡视觉冲击力，营造专业而不失活力的氛围。  

2. **功能与美学的平衡**  
   - **信息架构**：参考清华大学和中国美术学院的布局逻辑，首页采用“一屏式”设计，顶部悬浮导航栏分类清晰（如“学校概况”“教学资源”“家校互动”），重点展示校园实景图、新闻动态及核心功能入口。  
   - **响应式设计**：适配手机、平板等设备，确保移动端操作流畅（如折叠式菜单、触控优化按钮）。

---

### **二、核心功能模块**  
1. **信息展示模块**  
   - **学校概况**：嵌入校园全景图或3D漫游视频（参考网页2中知德中学的生态校园设计），介绍“一中心、两轴、三分区”的布局理念，突出绿色、智慧、生态特色。
   在导航栏添加一个“校影”，点击后跳转到新页面， 该页面放置全景照片，其中照片由123图床中下载并加载，添加一个图片上传的按钮，按钮只允许管理员看见并且使用，上传的新的全景图片，会自动添加到该页面，其全景功能不变。
   上传时可以添加标题，内容，摆设时间等信息，网站能自动添加。
   添加一件停止功能按钮（只允许管理员看见和使用），停止后，除管理员外，所有用户显示功能正在维护中，此时不下载加载全景照片。
   添加删除全景照片按钮（只允许管理员看见和使用） 
   - **新闻动态**：设置轮播图展示学校活动、荣誉奖项，支持按标签分类（如“教学成果”“学生风采”）。


3. **家校互动模块**  
   - **校友平台**：建立校友数据库，发布校友活动信息，增强社群归属感。
   在顶部添加一个“校评”按钮，点击后跳转到新页面，该页面为评论页面（只允许登录后的用户添加评论）
   添加评论功能，样式为长方形框，右下角有加号按钮，点击显示弹窗可以添加评论，有标题，内容等，内容下方有个添加图片的按钮，用户可以从相册中上传照片带着评论内容。
   注意图片与评论内容的绑定，所有上传的图片保存在123图床的imags文件夹中。
   发送的评论框可以自定义颜色。在上传图片的按钮旁添加自定义颜色的按钮，点击后有个小的颜色轮盘，用户可以通过颜色轮盘调节评论框颜色（让添加消息的弹窗周围跟随用户选择颜色变化而变化，以方便用户预览评论框的颜色变化）。
   每条评论右下角添加点赞按钮和“踩”按钮（注意统计点赞或“踩”的数据在旁边显示）记录用户点没点赞，即不要让用户重复点击和重复统计数据。
   点击评论后会向下展开评论的回复（注意展开动画，下方的其他评论会随着展开动画向下移动）。
   点击一级评论后会在下方显示一个评论框，点击后编写回复内容，不可上传图片。
   若该条评论已有回复点击后会优先加载点赞最多的五条评论，其次是最新时间发布的，若用户想看更多的回复，则用户可以点击最后一条回复下的“展开更多回复”继续加载五条回复
   给回复的内容也添加点赞和“踩”的功能
   给所有的评论添加删除按钮（位置在每个评论框的右上角，包括回复），按钮只允许发布者自己或管理员才被看见和使用。
   在评论页最上方添加一个搜索框，用户可以通过搜索评论中的标题或内容关键词进行快速查看评论。在搜索框旁添加排序按钮，可以选择按时间（从新到旧，从上往下），按点赞数量（从多到少，从上到下）进行排列评论
   注意保存用户评论的草稿，防止用户因手误关闭评论的弹窗，而丢失编辑的内容
   注意限制一次性加载评论的数量为10条（回复的一次性加载我在上面已提出要求为5个），防止同时加载导致服务器卡顿，当用户滑到底部最后一条评论时继续加载10个
   添加回到顶部的功能按钮，当用户下滑超过5条评论后就显示，否则不显示。

### **三、视觉与交互设计**  
1. **首页设计**  
   - **首屏视觉**：以紫色渐变背景为底，叠加白色校徽图案，背景图轮播展示校园生态景观（如绿树成荫的走廊、智慧教室场景）。  
   - **动态交互**：采用视差滚动效果，滑动时图片与文字分层移动，增强沉浸感（参考康奈尔大学的动态视频设计）。

2. **导航与排版**  
   - **导航栏设计**：悬浮式导航栏固定于顶部，分类清晰（如“关于我们”“教学”“招生”“联系”），二级菜单采用下拉式布局。  
   - **内容区块**：使用卡片式设计展示新闻、活动，每张卡片配以缩略图与摘要，点击后跳转至详情页。
在左上角顶部显示校徽图片,右边添加文字“重庆市梁平区知德中学”
主页正上方，导航栏下方显示一段随时间判断的欢迎语，如6：00-11：00显示“上午好，同学”，11：00-13：00显示“中午好，同学”，13：00-20：00显示“下午好同学”20：00-24：00显示“晚上好同学”0：00-6：00显示“天黑了，多休息，同学（所有文字加粗（醒目）并添加类似打字机的特效逐个快速显示，在用户进入网站后显示。

3. **多媒体整合**    
   - 

---

### **四、技术实现建议**  


2. **安全与维护**  
   - 后台管理系统支持权限分级（如管理员、教师、学生不同角色），操作日志记录功能。
在主页右上角顶部栏用户名字旁显示后台按钮，只允许管理员显示和进入
在用户列表中添加搜索框，可以通过搜索用户名对用户进行管理
头衔功能：管理员可以给其他用户添加一个头衔，可以自定义头衔颜色，用户在发送评论或回复时在其名字旁显示，用户也可以自由选择是否带头衔发布。
添加禁言功能，被禁言后的用户在评论页的添加按钮显示灰色，点击提示“您被管理员禁言，请联系管理员”（回复也不行），但可以点赞或“踩”
---

### **五、差异化亮点**  
1. **生态与智慧主题**：通过“绿色校园”专题页展示节能建筑、智能安防等特色，呼应知德中学的设计理念。  
2. **南开合作专区**：开设专栏介绍与南开中学的师资培训项目，突出教育资源优势。  
3. **学生创作展示**：设置“学生作品库”，收录艺术、科技项目成果，增强参与感。

---

### **六、实施步骤**  
1. **需求调研**：与校方、师生沟通，明确优先级功能（如家校互动为刚需）。  
2. **原型设计**：使用Figma或Axure制作高保真原型，确认交互逻辑。  
3. **开发与测试**：分阶段开发核心模块，进行多设备兼容性测试。  
4. **内容填充**：由校方提供图文素材，优化SEO关键词（如“知德中学智慧校园”）。  
所有弹窗简洁美观

通过以上方案，知德中学网站可实现品牌形象展示、教学支持与社群互动的全方位功能，同时体现紫色与白色的视觉特色。具体技术细节可参考网页9的Java系统源码及网页7的交互设计指南。