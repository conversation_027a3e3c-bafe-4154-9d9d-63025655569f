# 列举已上传分片（非必需）

API： POST 域名 + /upload/v1/file/list_upload_parts

> 说明：该接口用于最后一片分片上传完成时，列出云端分片供用户自行比对。比对正确后调用上传完毕接口。当文件大小小于 sliceSize 分片大小时，无需调用该接口，该结果将返回空值。
>

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| preuploadID | string | 必填 | 预备上传ID |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| parts | array | 必填 | 分片列表 |
| parts[*].partNumber | number | 必填 | 分片编号 |
| parts[*].size | number | 必填 | 分片大小 |
| parts[*].etag | string | 必填 | 分片md5 |


## 示例
请求示例

```shell
curl --location 'https://open-api.123pan.com/upload/v1/file/list_upload_parts' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "preuploadID": "WvjyUgonimrlBq22FJ9e33be4pi04nxxWefNRwRKIqRMBxZgRxyNJ...(过长省略)"
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
  .build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"preuploadID\": \"WvjyUgonimrlBq22FJ9e33be4pi04nxxWefNRwRKIqRMBxZgRxyNJ...(过长省略)\"\n}");
Request request = new Request.Builder()
  .url("https://open-api.123pan.com/upload/v1/file/list_upload_parts")
  .method("POST", body)
  .addHeader("Content-Type", "application/json")
  .addHeader("Platform", "open_platform")
  .addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
  .build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/upload/v1/file/list_upload_parts",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "preuploadID": "WvjyUgonimrlBq22FJ9e33be4pi04nxxWefNRwRKIqRMBxZgRxyNJ...(过长省略)"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "preuploadID": "WvjyUgonimrlBq22FJ9e33be4pi04nxxWefNRwRKIqRMBxZgRxyNJ...(过长省略)"
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/upload/v1/file/list_upload_parts',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
    "preuploadID": "WvjyUgonimrlBq22FJ9e33be4pi04nxxWefNRwRKIqRMBxZgRxyNJ...(过长省略)"
})
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("POST", "/upload/v1/file/list_upload_parts", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

响应示例

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "parts": [
      {
        "partNumber": "1",
        "size": 16777216,
        "etag": "0ed7db00127b178c21e288416863caa5"
      }
    ]
  },
  "x-traceID": "65021f5f-7e59-4f72-a080-8db6d8ee9398_kong-db-5898fdd8c6-wnv6h"
}
```



> 更新: 2025-03-17 19:16:11  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/vfciz4tmloogx6b6>