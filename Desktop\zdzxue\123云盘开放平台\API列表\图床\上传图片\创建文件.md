# 创建文件

API： POST   域名 + /upload/v1/oss/file/create

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| parentFileID | string | 必填 | 父目录id，上传到根目录时填写 空 |
| filename | string | 必填 | 文件名要小于255个字符且不能包含以下任何字符："\/:*?|><。（注：不能重名） |
| etag | string | 必填 | 文件md5 |
| size | number | 必填 | 文件大小，单位为 byte 字节 |
| <font style="color:#000000;">type</font> | <font style="color:#000000;">number</font> | <font style="color:#000000;">必填</font> | <font style="color:#000000;">固定为 1</font> |


## 返回数据
| **<font style="color:#000000;">名称</font>** | **<font style="color:#000000;">类型</font>** | **<font style="color:#000000;">是否必填</font>** | **<font style="color:#000000;">说明</font>** |
| :---: | :---: | :---: | --- |
| fileID | string | 非必填 | 文件ID。当123云盘已有该文件,则会发生秒传。此时会将文件ID字段返回。唯一 |
| preuploadID | string | 必填 | 预上传ID(如果 reuse 为 true 时,该字段不存在) |
| reuse | boolean | 必填 | 是否秒传，返回true时表示文件已上传成功 |
| sliceSize | number | 必填 | 分片大小，必须按此大小生成文件分片再上传 |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/upload/v1/oss/file/create' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "parentFileID": "yk6baz03t0l000d7w33fbyq51l4izkneDIYPAIDOBIY0DcxvDwFO",
    "filename": "测试图床.jpg",
    "etag": "e62623f4906aeba8f8d8f5de19e1e34e",
    "size": 22032384,
    "type": 1
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"parentFileID\": \"yk6baz03t0l000d7w33fbyq51l4izkneDIYPAIDOBIY0DcxvDwFO\",\n    \"filename\": \"测试图床.jpg\",\n    \"etag\": \"e62623f4906aeba8f8d8f5de19e1e34e\",\n    \"size\": 22032384,\n    \"type\": 1\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/upload/v1/oss/file/create")
.method("POST", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/upload/v1/oss/file/create",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "parentFileID": "yk6baz03t0l000d7w33fbyq51l4izkneDIYPAIDOBIY0DcxvDwFO",
    "filename": "测试图床.jpg",
    "etag": "e62623f4906aeba8f8d8f5de19e1e34e",
    "size": 22032384,
    "type": 1
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "parentFileID": "yk6baz03t0l000d7w33fbyq51l4izkneDIYPAIDOBIY0DcxvDwFO",
  "filename": "测试图床.jpg",
  "etag": "e62623f4906aeba8f8d8f5de19e1e34e",
  "size": 22032384,
  "type": 1
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/upload/v1/oss/file/create',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
  "parentFileID": "yk6baz03t0l000d7w33fbyq51l4izkneDIYPAIDOBIY0DcxvDwFO",
  "filename": "测试图床.jpg",
  "etag": "e62623f4906aeba8f8d8f5de19e1e34e",
  "size": 22032384,
  "type": 1
})
headers = {
  'Content-Type': 'application/json',
  'Platform': 'open_platform',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("POST", "/upload/v1/oss/file/create", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "fileID": "",
    "reuse": false,
    "preuploadID": "h1Kiaaaaaaac/0IDD87IFbIf8T0UWrTNwNNGbGoeklBYFtnlDwBIhd9OfdMjm4abJfDPccrScqQIPdjFasHxGxV//V7bzfUbEEaEt8N6RT2PI/dC/gv...(过长省略)",
    "sliceSize": 104857600
  },
  "x-traceID": "854f0197-36a9-4367-8b46-206a377f4327_kong-db-5898fdd8c6-t5pvc"
}
```



> 更新: 2025-03-17 19:17:26  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/xwfka5kt6vtmgs8r>