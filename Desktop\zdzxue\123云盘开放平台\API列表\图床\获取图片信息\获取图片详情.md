# 获取图片详情

API： GET 域名 + /api/v1/oss/file/detail

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## QueryString 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| fileID | string | 必填 | 文件ID |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| fileId | string | 必填 | 文件ID |
| filename | string | 必填 | 文件名 |
| type | number | 必填 | 0-文件  1-文件夹 |
| size | number | 必填 | 文件大小 |
| etag | string | 必填 | md5 |
| status | number | 必填 | 文件审核状态。 大于 100 为审核驳回文件 |
| createAt | string | 必填 | 创建时间 |
| updateAt | string | 必填 | 更新时间 |
| downloadURL | string | 必填 | 下载链接 |
| userSelfURL | string | 必填 | 自定义域名链接 |
| totalTraffic | number | 必填 | 流量统计 |
| parentFileId | string | 必填 | 父级ID |
| parentFilename | string | 必填 | 父级文件名称 |
| extension | string | 必填 | 后缀名称 |


## 示例
**请求示例**

```shell
curl --location --request GET 'https://open-api.123pan.com/api/v1/oss/file/detail' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "fileID": "ymjew503t0m000d7w32xormjidkak3rgDIYPAIDOBIY0DcxvDwFO"
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"fileID\": \"ymjew503t0m000d7w32xormjidkak3rgDIYPAIDOBIY0DcxvDwFO\"\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/oss/file/detail")
.method("GET", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/oss/file/detail",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "fileID": "ymjew503t0m000d7w32xormjidkak3rgDIYPAIDOBIY0DcxvDwFO"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "fileID": "ymjew503t0m000d7w32xormjidkak3rgDIYPAIDOBIY0DcxvDwFO"
});

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/oss/file/detail',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
  "fileID": "ymjew503t0m000d7w32xormjidkak3rgDIYPAIDOBIY0DcxvDwFO"
})
headers = {
  'Content-Type': 'application/json',
  'Platform': 'open_platform',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("GET", "/api/v1/oss/file/detail", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "fileId": "ymjew503t0m000d7w32xormjidkak3rgDIYPAIDOBIY0DcxvDwFO",
        "filename": "测试图床.jpg",
        "parentFileId": "yk6baz03t0l000d7w33fbyq51l4izkneDIYPAIDOBIY0DcxvDwFO",
        "type": 0,
        "etag": "e62623f4906aeba8f8d8f5de19e1e34e",
        "size": 22027358,
        "status": 2,
        "s3KeyFlag": "1817178140-0",
        "storageNode": "m76",
        "createAt": "2025-03-03 16:38:26",
        "updateAt": "2025-03-03 16:38:26",
        "downloadURL": "https://vip.123pan.cn/1815309870/ymjew503t0m000d7w32xormjidkak3rgDIYPAIDOBIY0DcxvDwFO.jpg",
        "ossIndex": 43,
        "totalTraffic": 0,
        "parentFilename": "测试图床目录",
        "extension": "jpg",
        "userSelfURL": "https://vip.123pan.cn/1815309870/ymjew503t0m000d7w32xormjidkak3rgDIYPAIDOBIY0DcxvDwFO.jpg"
    },
    "x-traceID": "fe8e8c6e-e7bd-44f2-bc83-3243e7e07ee5_kong-db-5898fdd8c6-wgsts"
}
```



> 更新: 2025-03-17 19:17:35  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/rgf2ndfaxc2gugp8>