/**
 * 评论系统核心功能
 * 处理评论的增删改查、点赞、回复等功能
 */

class CommentSystem {
    constructor() {
        // 评论数据存储（实际项目中应该使用数据库）
        this.comments = [];
        this.currentUser = null;
        
        // 分页配置
        this.pageSize = 10;
        this.currentPage = 1;
        this.totalComments = 0;
        
        // 排序配置
        this.sortBy = 'time-desc'; // time-desc, time-asc, likes-desc, likes-asc
        
        // 搜索配置
        this.searchKeyword = '';
        
        // 草稿存储
        this.draftKey = 'school_review_draft';
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化评论系统
     */
    init() {
        // 加载本地存储的评论数据
        this.loadCommentsFromStorage();
        
        // 加载草稿
        this.loadDraft();
        
        // 初始化示例数据（仅用于演示）
        if (this.comments.length === 0) {
            this.initSampleData();
        }
        
        console.log('评论系统初始化完成');
    }
    
    /**
     * 从本地存储加载评论数据
     */
    loadCommentsFromStorage() {
        try {
            const stored = localStorage.getItem('school_review_comments');
            if (stored) {
                this.comments = JSON.parse(stored);
                this.totalComments = this.comments.length;
            }
        } catch (error) {
            console.error('加载评论数据失败:', error);
            this.comments = [];
        }
    }
    
    /**
     * 保存评论数据到本地存储
     */
    saveCommentsToStorage() {
        try {
            localStorage.setItem('school_review_comments', JSON.stringify(this.comments));
        } catch (error) {
            console.error('保存评论数据失败:', error);
        }
    }
    
    /**
     * 初始化示例数据
     */
    initSampleData() {
        const sampleComments = [
            {
                id: 'comment_1',
                title: '学校食堂的新菜品真不错！',
                content: '今天在食堂吃到了新推出的川菜，味道很正宗，价格也很实惠。希望学校能继续丰富菜品种类，满足不同地区同学的口味需求。',
                author: {
                    uid: 'UID_123456',
                    username: 'admin',
                    avatar: '../images/avatar.png'
                },
                images: [],
                createTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2小时前
                likes: 15,
                dislikes: 2,
                replies: [
                    {
                        id: 'reply_1_1',
                        content: '同感！我也很喜欢新的川菜，特别是麻婆豆腐。',
                        author: {
                            uid: 'UID_654321',
                            username: 'test',
                            avatar: '../images/avatar.png'
                        },
                        createTime: new Date(Date.now() - 1.5 * 60 * 60 * 1000).toISOString(),
                        replyTo: 'comment_1'
                    }
                ],
                userReaction: null, // null, 'like', 'dislike'
                backgroundColor: '#f8f9fa'
            },
            {
                id: 'comment_2',
                title: '图书馆的学习环境越来越好了',
                content: '最近图书馆新增了很多座位，还安装了新的空调系统。学习环境比以前舒适多了，希望大家都能爱护公共设施。',
                author: {
                    uid: 'UID_789012',
                    username: '学习委员',
                    avatar: '../images/avatar.png'
                },
                images: [],
                createTime: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), // 5小时前
                likes: 28,
                dislikes: 1,
                replies: [],
                userReaction: null,
                backgroundColor: '#e8f5e8'
            },
            {
                id: 'comment_3',
                title: '运动会准备得怎么样了？',
                content: '听说下个月要举办校运动会，大家都准备参加什么项目呢？我想报名参加1500米长跑，有一起训练的同学吗？',
                author: {
                    uid: 'UID_345678',
                    username: '体育爱好者',
                    avatar: '../images/avatar.png'
                },
                images: [],
                createTime: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8小时前
                likes: 12,
                dislikes: 0,
                replies: [
                    {
                        id: 'reply_3_1',
                        content: '我也想参加长跑！我们可以一起训练。',
                        author: {
                            uid: 'UID_456789',
                            username: '跑步达人',
                            avatar: '../images/avatar.png'
                        },
                        createTime: new Date(Date.now() - 7 * 60 * 60 * 1000).toISOString(),
                        replyTo: 'comment_3'
                    },
                    {
                        id: 'reply_3_2',
                        content: '我准备参加篮球比赛，有组队的吗？',
                        author: {
                            uid: 'UID_567890',
                            username: '篮球小子',
                            avatar: '../images/avatar.png'
                        },
                        createTime: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
                        replyTo: 'comment_3'
                    }
                ],
                userReaction: null,
                backgroundColor: '#fff3cd'
            }
        ];
        
        this.comments = sampleComments;
        this.totalComments = sampleComments.length;
        this.saveCommentsToStorage();
    }
    
    /**
     * 设置当前用户
     * @param {Object} user - 用户对象
     */
    setCurrentUser(user) {
        this.currentUser = user;
    }
    
    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    generateId() {
        return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * 格式化时间
     * @param {string} isoString - ISO时间字符串
     * @returns {string} 格式化后的时间
     */
    formatTime(isoString) {
        const date = new Date(isoString);
        const now = new Date();
        const diff = now - date;
        
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        if (minutes < 1) {
            return '刚刚';
        } else if (minutes < 60) {
            return `${minutes}分钟前`;
        } else if (hours < 24) {
            return `${hours}小时前`;
        } else if (days < 7) {
            return `${days}天前`;
        } else {
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }
    }
    
    /**
     * 添加新评论
     * @param {Object} commentData - 评论数据
     * @returns {Object} 添加结果
     */
    addComment(commentData) {
        if (!this.currentUser) {
            return {
                success: false,
                error: '请先登录后再发表评论'
            };
        }
        
        // 验证评论数据
        if (!commentData.title || !commentData.content) {
            return {
                success: false,
                error: '标题和内容不能为空'
            };
        }
        
        if (commentData.title.length > 100) {
            return {
                success: false,
                error: '标题不能超过100个字符'
            };
        }
        
        if (commentData.content.length > 1000) {
            return {
                success: false,
                error: '内容不能超过1000个字符'
            };
        }
        
        // 创建新评论
        const newComment = {
            id: this.generateId(),
            title: commentData.title.trim(),
            content: commentData.content.trim(),
            author: {
                uid: this.currentUser.uid,
                username: this.currentUser.username,
                avatar: this.currentUser.avatar
            },
            images: commentData.images || [],
            createTime: new Date().toISOString(),
            likes: 0,
            dislikes: 0,
            replies: [],
            userReaction: null,
            backgroundColor: commentData.backgroundColor || '#ffffff'
        };
        
        // 添加到评论列表开头
        this.comments.unshift(newComment);
        this.totalComments++;
        
        // 保存到本地存储
        this.saveCommentsToStorage();
        
        // 清除草稿
        this.clearDraft();
        
        return {
            success: true,
            comment: newComment
        };
    }
    
    /**
     * 删除评论
     * @param {string} commentId - 评论ID
     * @returns {Object} 删除结果
     */
    deleteComment(commentId) {
        if (!this.currentUser) {
            return {
                success: false,
                error: '请先登录'
            };
        }
        
        const commentIndex = this.comments.findIndex(c => c.id === commentId);
        if (commentIndex === -1) {
            return {
                success: false,
                error: '评论不存在'
            };
        }
        
        const comment = this.comments[commentIndex];
        
        // 检查权限（只能删除自己的评论）
        if (comment.author.uid !== this.currentUser.uid) {
            return {
                success: false,
                error: '只能删除自己的评论'
            };
        }
        
        // 删除评论
        this.comments.splice(commentIndex, 1);
        this.totalComments--;
        
        // 保存到本地存储
        this.saveCommentsToStorage();
        
        return {
            success: true
        };
    }
    
    /**
     * 添加回复
     * @param {string} commentId - 评论ID
     * @param {string} content - 回复内容
     * @returns {Object} 添加结果
     */
    addReply(commentId, content) {
        if (!this.currentUser) {
            return {
                success: false,
                error: '请先登录后再回复'
            };
        }
        
        if (!content || content.trim().length === 0) {
            return {
                success: false,
                error: '回复内容不能为空'
            };
        }
        
        if (content.length > 500) {
            return {
                success: false,
                error: '回复内容不能超过500个字符'
            };
        }
        
        const comment = this.comments.find(c => c.id === commentId);
        if (!comment) {
            return {
                success: false,
                error: '评论不存在'
            };
        }
        
        // 创建新回复
        const newReply = {
            id: this.generateId(),
            content: content.trim(),
            author: {
                uid: this.currentUser.uid,
                username: this.currentUser.username,
                avatar: this.currentUser.avatar
            },
            createTime: new Date().toISOString(),
            replyTo: commentId
        };
        
        // 添加回复
        comment.replies.push(newReply);
        
        // 保存到本地存储
        this.saveCommentsToStorage();
        
        return {
            success: true,
            reply: newReply
        };
    }
    
    /**
     * 点赞/踩评论
     * @param {string} commentId - 评论ID
     * @param {string} action - 操作类型 ('like' 或 'dislike')
     * @returns {Object} 操作结果
     */
    reactToComment(commentId, action) {
        if (!this.currentUser) {
            return {
                success: false,
                error: '请先登录'
            };
        }
        
        const comment = this.comments.find(c => c.id === commentId);
        if (!comment) {
            return {
                success: false,
                error: '评论不存在'
            };
        }
        
        // 获取用户之前的反应
        const previousReaction = comment.userReaction;
        
        // 更新反应
        if (previousReaction === action) {
            // 如果点击的是相同的按钮，则取消反应
            comment.userReaction = null;
            if (action === 'like') {
                comment.likes = Math.max(0, comment.likes - 1);
            } else {
                comment.dislikes = Math.max(0, comment.dislikes - 1);
            }
        } else {
            // 如果点击的是不同的按钮
            if (previousReaction) {
                // 先取消之前的反应
                if (previousReaction === 'like') {
                    comment.likes = Math.max(0, comment.likes - 1);
                } else {
                    comment.dislikes = Math.max(0, comment.dislikes - 1);
                }
            }
            
            // 添加新的反应
            comment.userReaction = action;
            if (action === 'like') {
                comment.likes++;
            } else {
                comment.dislikes++;
            }
        }
        
        // 保存到本地存储
        this.saveCommentsToStorage();
        
        return {
            success: true,
            comment: comment
        };
    }
    
    /**
     * 搜索评论
     * @param {string} keyword - 搜索关键词
     */
    searchComments(keyword) {
        this.searchKeyword = keyword.trim();
        this.currentPage = 1; // 重置到第一页
    }
    
    /**
     * 设置排序方式
     * @param {string} sortBy - 排序方式
     */
    setSortBy(sortBy) {
        this.sortBy = sortBy;
        this.currentPage = 1; // 重置到第一页
    }
    
    /**
     * 获取过滤和排序后的评论列表
     * @returns {Array} 评论列表
     */
    getFilteredComments() {
        let filteredComments = [...this.comments];
        
        // 搜索过滤
        if (this.searchKeyword) {
            const keyword = this.searchKeyword.toLowerCase();
            filteredComments = filteredComments.filter(comment => 
                comment.title.toLowerCase().includes(keyword) ||
                comment.content.toLowerCase().includes(keyword) ||
                comment.author.username.toLowerCase().includes(keyword)
            );
        }
        
        // 排序
        filteredComments.sort((a, b) => {
            switch (this.sortBy) {
                case 'time-asc':
                    return new Date(a.createTime) - new Date(b.createTime);
                case 'time-desc':
                    return new Date(b.createTime) - new Date(a.createTime);
                case 'likes-asc':
                    return a.likes - b.likes;
                case 'likes-desc':
                    return b.likes - a.likes;
                default:
                    return new Date(b.createTime) - new Date(a.createTime);
            }
        });
        
        return filteredComments;
    }
    
    /**
     * 获取分页评论
     * @param {number} page - 页码
     * @returns {Object} 分页结果
     */
    getPagedComments(page = 1) {
        const filteredComments = this.getFilteredComments();
        const totalFiltered = filteredComments.length;
        
        const startIndex = (page - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        
        const pagedComments = filteredComments.slice(startIndex, endIndex);
        
        return {
            comments: pagedComments,
            currentPage: page,
            totalPages: Math.ceil(totalFiltered / this.pageSize),
            totalComments: totalFiltered,
            hasMore: endIndex < totalFiltered
        };
    }
    
    /**
     * 加载更多评论
     * @returns {Object} 加载结果
     */
    loadMoreComments() {
        this.currentPage++;
        return this.getPagedComments(this.currentPage);
    }
    
    /**
     * 保存草稿
     * @param {Object} draftData - 草稿数据
     */
    saveDraft(draftData) {
        try {
            localStorage.setItem(this.draftKey, JSON.stringify({
                ...draftData,
                timestamp: Date.now()
            }));
        } catch (error) {
            console.error('保存草稿失败:', error);
        }
    }
    
    /**
     * 加载草稿
     * @returns {Object|null} 草稿数据
     */
    loadDraft() {
        try {
            const stored = localStorage.getItem(this.draftKey);
            if (stored) {
                const draft = JSON.parse(stored);
                // 检查草稿是否过期（24小时）
                if (Date.now() - draft.timestamp < 24 * 60 * 60 * 1000) {
                    return draft;
                } else {
                    this.clearDraft();
                }
            }
        } catch (error) {
            console.error('加载草稿失败:', error);
        }
        return null;
    }
    
    /**
     * 清除草稿
     */
    clearDraft() {
        try {
            localStorage.removeItem(this.draftKey);
        } catch (error) {
            console.error('清除草稿失败:', error);
        }
    }
    
    /**
     * 获取评论统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const totalLikes = this.comments.reduce((sum, comment) => sum + comment.likes, 0);
        const totalReplies = this.comments.reduce((sum, comment) => sum + comment.replies.length, 0);
        
        return {
            totalComments: this.totalComments,
            totalLikes: totalLikes,
            totalReplies: totalReplies,
            averageLikes: this.totalComments > 0 ? (totalLikes / this.totalComments).toFixed(1) : 0
        };
    }
    
    /**
     * 导出评论数据
     * @returns {string} JSON格式的评论数据
     */
    exportComments() {
        return JSON.stringify(this.comments, null, 2);
    }
    
    /**
     * 导入评论数据
     * @param {string} jsonData - JSON格式的评论数据
     * @returns {Object} 导入结果
     */
    importComments(jsonData) {
        try {
            const importedComments = JSON.parse(jsonData);
            if (Array.isArray(importedComments)) {
                this.comments = importedComments;
                this.totalComments = importedComments.length;
                this.saveCommentsToStorage();
                return {
                    success: true,
                    message: `成功导入 ${importedComments.length} 条评论`
                };
            } else {
                throw new Error('数据格式不正确');
            }
        } catch (error) {
            return {
                success: false,
                error: '导入失败: ' + error.message
            };
        }
    }
}

// 导出评论系统类
window.CommentSystem = CommentSystem;
