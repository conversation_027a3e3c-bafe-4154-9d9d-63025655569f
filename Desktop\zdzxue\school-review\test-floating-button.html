<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>悬浮按钮测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #6c5ce7;
            --hover-color: #5a4fcf;
            --secondary-color: #ffffff;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: var(--hover-color);
            transform: translateY(-2px);
        }
        
        .status {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        /* 悬浮按钮样式 */
        .floating-add-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), var(--hover-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(108, 92, 231, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            color: var(--secondary-color);
            font-size: 1.5rem;
        }
        
        .floating-add-btn:hover {
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 12px 35px rgba(108, 92, 231, 0.4);
            background: linear-gradient(135deg, var(--hover-color), var(--primary-color));
        }
        
        .floating-add-btn:active {
            transform: translateY(-1px) scale(1.05);
        }
        
        .floating-add-btn i {
            transition: transform 0.3s ease;
        }
        
        .floating-add-btn:hover i {
            transform: rotate(90deg);
        }
        
        /* Toast提示样式 */
        .toast-notification {
            position: fixed;
            top: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 0.95rem;
            font-weight: 500;
            z-index: 3000;
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: none;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }
        
        .toast-notification.show {
            opacity: 1;
            transform: translateX(-50%) translateY(10px);
        }
        
        .toast-notification.warning {
            background: rgba(243, 156, 18, 0.9);
        }
        
        .toast-notification.success {
            background: rgba(39, 174, 96, 0.9);
        }
        
        .toast-notification.info {
            background: rgba(52, 152, 219, 0.9);
        }
        
        /* 移动端适配 */
        @media (max-width: 480px) {
            .floating-add-btn {
                bottom: 20px;
                right: 20px;
                width: 50px;
                height: 50px;
                font-size: 1.3rem;
            }
            
            .toast-notification {
                top: 80px;
                left: 20px;
                right: 20px;
                transform: none;
                font-size: 0.9rem;
                padding: 10px 20px;
            }
            
            .toast-notification.show {
                transform: translateY(10px);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 悬浮按钮和Toast提示测试</h1>
        
        <div class="test-section">
            <h3>1. 悬浮按钮测试</h3>
            <p>右下角应该显示一个紫色的圆形加号按钮</p>
            <div class="status info">
                ✅ 悬浮按钮位置：屏幕右下角<br>
                ✅ 悬浮按钮样式：紫色渐变圆形<br>
                ✅ 悬浮按钮图标：加号 (+)<br>
                ✅ 悬浮按钮动画：悬停时放大和旋转
            </div>
        </div>
        
        <div class="test-section">
            <h3>2. Toast提示测试</h3>
            <p>点击下面的按钮测试不同类型的Toast提示</p>
            <button class="test-button" onclick="showToast('请先登录', 'warning')">警告提示</button>
            <button class="test-button" onclick="showToast('操作成功！', 'success')">成功提示</button>
            <button class="test-button" onclick="showToast('这是一条信息', 'info')">信息提示</button>
            <div id="toastStatus" class="status info">点击上方按钮测试Toast提示效果</div>
        </div>
        
        <div class="test-section">
            <h3>3. 登录状态模拟</h3>
            <p>模拟登录状态来测试悬浮按钮的行为</p>
            <button class="test-button" onclick="simulateLogin()">模拟登录</button>
            <button class="test-button" onclick="simulateLogout()">模拟退出</button>
            <div id="loginStatus" class="status info">当前状态：未登录</div>
        </div>
        
        <div class="test-section">
            <h3>4. 响应式测试</h3>
            <p>调整浏览器窗口大小测试移动端适配</p>
            <div class="status info">
                📱 移动端 (≤480px)：<br>
                - 悬浮按钮变小 (50x50px)<br>
                - Toast提示占满屏幕宽度<br>
                - 位置和动画自动调整
            </div>
        </div>
        
        <div class="test-section">
            <h3>5. 实际页面测试</h3>
            <p>在实际的校评页面中测试功能</p>
            <button class="test-button" onclick="openSchoolReview()">打开校评页面</button>
            <div class="status info">
                测试步骤：<br>
                1. 在未登录状态下点击悬浮按钮<br>
                2. 应该显示"请先登录"的Toast提示<br>
                3. 登录后再次点击应该打开评论弹窗
            </div>
        </div>
    </div>
    
    <!-- 悬浮按钮 -->
    <div class="floating-add-btn" id="floatingBtn" onclick="handleFloatingBtnClick()">
        <i class="fas fa-plus"></i>
    </div>
    
    <script>
        let isLoggedIn = false;
        
        // 显示Toast提示
        function showToast(message, type = 'info') {
            // 移除已存在的toast
            const existingToast = document.querySelector('.toast-notification');
            if (existingToast) {
                existingToast.remove();
            }
            
            // 创建新的toast
            const toast = document.createElement('div');
            toast.className = `toast-notification ${type}`;
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 2000);
            
            // 更新状态显示
            const statusDiv = document.getElementById('toastStatus');
            statusDiv.innerHTML = `✅ 显示了${type}类型的Toast: "${message}"`;
        }
        
        // 处理悬浮按钮点击
        function handleFloatingBtnClick() {
            if (!isLoggedIn) {
                showToast('请先登录', 'warning');
            } else {
                showToast('打开评论编辑器', 'success');
            }
        }
        
        // 模拟登录
        function simulateLogin() {
            isLoggedIn = true;
            const statusDiv = document.getElementById('loginStatus');
            statusDiv.className = 'status success';
            statusDiv.innerHTML = '✅ 当前状态：已登录 (测试用户)';
            showToast('登录成功！', 'success');
        }
        
        // 模拟退出
        function simulateLogout() {
            isLoggedIn = false;
            const statusDiv = document.getElementById('loginStatus');
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '当前状态：未登录';
            showToast('已退出登录', 'info');
        }
        
        // 打开校评页面
        function openSchoolReview() {
            window.open('index.html', '_blank');
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            setTimeout(() => {
                showToast('测试页面加载完成！', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
