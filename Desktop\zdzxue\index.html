<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重庆市梁平区知德中学</title>
    <link rel="stylesheet" href="css/style.css">
    <!-- 添加字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <header>
        <nav class="navbar">
            <div class="logo-container">
                <img src="images/logo.jpg" alt="知德中学校徽" class="logo">
                <h1 class="school-name">重庆市梁平区知德中学</h1>
            </div>
            <div class="nav-links">
                <a href="index.html" class="nav-link">首页</a>
                <a href="school-review/index.html" class="nav-link">校评</a>
            </div>
            <div class="user-actions">
                <div class="user-avatar" id="avatarContainer" style="display: none;">
                    <img src="images/avatar.png" alt="用户头像" id="userAvatar">
                </div>
                <button class="login-btn" id="loginBtn">登录</button>
            </div>
        </nav>
    </header>

    <!-- 欢迎语 -->
    <section class="welcome-section">
        <div class="welcome-text" id="welcomeText"></div>
    </section>

    <!-- 学校简介 -->
    <section class="intro-section">
        <div class="container">
            <h2 class="section-title">学校简介</h2>
            <img src="images/top.jpg" alt="知德中学全景" class="campus-panorama">
            <div class="intro-content">
                <p>重庆市梁平区知德中学创建于2002年，占地150亩，建筑面积5.2万平方米。学校以"知行合一，德润人生"为办学理念，是梁平区首批重点中学，现有56个教学班，师生3000余人。</p>

                <p>学校构建"一中心两轴三区"布局：<br>
                • 教学中心：配备86间智慧教室和学科创新实验室<br>
                • 文化轴：连接校史馆与艺术中心<br>
                • 生态轴：贯穿樱花园、香樟林等景观<br>
                • 教学区/运动区/生活区三区联动</p>
            </div>
        </div>
    </section>

    <!-- 主要轮播图 -->
    <section class="main-carousel">
        <div class="carousel-container">
            <div class="carousel-slides">
                <div class="carousel-slide active">
                    <img src="images/banner1.jpg" alt="学校新闻1">
                    <div class="slide-caption">知德中学2023年秋季开学典礼隆重举行</div>
                </div>
                <div class="carousel-slide">
                    <img src="images/banner2.jpg" alt="学校新闻2">
                    <div class="slide-caption">我校学生在全国中学生科技创新大赛中获得金奖</div>
                </div>
                <div class="carousel-slide">
                    <img src="images/banner3.jpg" alt="学校新闻3">
                    <div class="slide-caption">知德中学与南开中学建立教育合作关系</div>
                </div>
            </div>
            <div class="carousel-controls">
                <button class="prev-btn"><i class="fas fa-chevron-left"></i></button>
                <div class="carousel-indicators">
                    <span class="indicator active" data-index="0"></span>
                    <span class="indicator" data-index="1"></span>
                    <span class="indicator" data-index="2"></span>
                </div>
                <button class="next-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
        </div>
    </section>

    <!-- 新闻动态 -->
    <section class="news-section">
        <div class="container">
            <h2 class="section-title">新闻动态</h2>
            <div class="news-filter">
                <button class="filter-btn active" data-filter="all">全部</button>
                <button class="filter-btn" data-filter="teaching">教学成果</button>
                <button class="filter-btn" data-filter="student">学生风采</button>
                <button class="filter-btn" data-filter="campus">校园活动</button>
            </div>
            <div class="news-grid">
                <div class="news-card" data-category="teaching">
                    <img src="images/news1.jpg" alt="教学成果新闻">
                    <div class="news-content">
                        <h3>我校教师在市级教学比赛中获得一等奖</h3>
                        <p class="news-date">2023-09-15</p>
                        <p class="news-excerpt">近日，在重庆市中学教师教学技能大赛中，我校李明老师凭借出色的表现获得一等奖...</p>
                        <a href="#" class="read-more">阅读更多</a>
                    </div>
                </div>
                <div class="news-card" data-category="student">
                    <img src="images/news2.jpg" alt="学生风采新闻">
                    <div class="news-content">
                        <h3>高三学子在全国数学竞赛中创佳绩</h3>
                        <p class="news-date">2023-09-10</p>
                        <p class="news-excerpt">在刚刚结束的全国高中数学竞赛中，我校高三(1)班张华同学获得省级一等奖...</p>
                        <a href="#" class="read-more">阅读更多</a>
                    </div>
                </div>
                <!-- 更多新闻卡片 -->
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <h3>重庆市梁平区知德中学</h3>
                    <p>地址：重庆市梁平区XXX路XX号</p>
                    <p>电话：023-XXXXXXXX</p>
                    <p>邮箱：<EMAIL></p>
                </div>
                <div class="footer-links">
                    <h3>快速链接</h3>
                    <ul>
                        <li><a href="#">学校概况</a></li>
                        <li><a href="#">招生信息</a></li>
                        <li><a href="#">校园新闻</a></li>
                        <li><a href="#">联系我们</a></li>
                    </ul>
                </div>
                <div class="footer-social">
                    <h3>关注我们</h3>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><img src="images/wechat.png" alt="微信"></a>
                        <a href="#" class="social-icon"><img src="images/weibo.png" alt="微博"></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 重庆市梁平区知德中学 版权所有</p>
            </div>
        </div>
    </footer>

    <!-- 登录弹窗 -->
    <div class="modal-overlay" id="loginModal">
        <div class="login-modal">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">用户登录</h3>
                <button class="close-modal" id="closeModal">&times;</button>
            </div>

            <div class="modal-tabs">
                <button class="modal-tab active" id="loginTab">登录</button>
                <button class="modal-tab" id="registerTab">注册</button>
            </div>

            <!-- 登录表单 -->
            <div class="form-tab active" id="loginForm">
                <div class="notification" id="loginNotification"></div>

                <div class="form-group">
                    <label for="loginUsername">用户名</label>
                    <input type="text" class="form-control" id="loginUsername" placeholder="请输入用户名">
                </div>

                <div class="form-group">
                    <label for="loginPassword">密码</label>
                    <input type="password" class="form-control" id="loginPassword" placeholder="请输入密码">
                </div>

                <div class="form-footer">
                    <div class="remember-me">
                        <input type="checkbox" id="rememberMe">
                        <label for="rememberMe">记住我</label>
                    </div>
                    <a href="#" class="forgot-password">忘记密码?</a>
                </div>

                <button class="submit-btn" id="loginButton">登录</button>

                <div class="register-prompt">
                    还没有账号? <a href="#" class="register-link" id="switchToRegister">立即注册</a>
                </div>
            </div>

            <!-- 注册表单 -->
            <div class="form-tab" id="registerForm">
                <div class="notification" id="registerNotification"></div>

                <div class="form-group">
                    <label for="registerUsername">用户名</label>
                    <input type="text" class="form-control" id="registerUsername" placeholder="请设置用户名">
                </div>

                <div class="form-group">
                    <label for="registerPassword">密码</label>
                    <input type="password" class="form-control" id="registerPassword" placeholder="请设置密码">
                </div>

                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <input type="password" class="form-control" id="confirmPassword" placeholder="请再次输入密码">
                </div>

                <button class="submit-btn" id="registerButton">注册</button>

                <div class="register-prompt">
                    已有账号? <a href="#" class="register-link" id="switchToLogin">立即登录</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 个人信息弹窗 -->
    <div class="modal-overlay" id="profileModal">
        <div class="profile-modal">
            <div class="modal-header">
                <h3 class="modal-title">个人信息</h3>
                <button class="close-modal" id="closeProfileModal">&times;</button>
            </div>

            <div class="profile-content">
                <div class="notification" id="profileNotification"></div>

                <div class="avatar-section">
                    <div class="current-avatar">
                        <img src="images/avatar.png" alt="当前头像" id="currentAvatar">
                    </div>
                    <div class="avatar-upload">
                        <label for="avatarUpload" class="avatar-upload-label">更换头像</label>
                        <input type="file" id="avatarUpload" accept="image/*" style="display: none;">
                    </div>
                </div>

                <div class="form-group">
                    <label for="profileUsername">用户名</label>
                    <input type="text" class="form-control" id="profileUsername" placeholder="请输入新用户名">
                </div>

                <div class="user-id-section">
                    <p>用户ID: <span id="userUniqueId">UID_XXXXX</span></p>
                    <p class="id-note">（此ID为您的唯一身份标识，无法修改）</p>
                </div>

                <div class="profile-buttons">
                    <button class="submit-btn" id="saveProfileButton">保存修改</button>
                    <button class="logout-btn" id="logoutButton">退出登录</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>