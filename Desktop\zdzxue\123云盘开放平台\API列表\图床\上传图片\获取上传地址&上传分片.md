# 获取上传地址&上传分片

API： POST 域名 + /upload/v1/oss/file/get_upload_url

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| preuploadID | string | 必填 | 预上传ID |
| sliceNo | number | 必填 | 分片序号，从1开始自增 |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| presignedURL | string | 必填 | 上传地址 |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/upload/v1/oss/file/get_upload_url' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "preuploadID": "h1Kiaaaaaaac/0IDD87IFbIf8T0UWrTNwNNGbGoeklBYFtnlDwBIhd9OfdMjm4abJfDPccrScqQIPdjFasHxGxV//V7bzfUbEEaEt8N6RT2PI/dC/gvyvf...(过长省略)",
    "sliceNo": 1
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"preuploadID\": \"h1Kiaaaaaaac/0IDD87IFbIf8T0UWrTNwNNGbGoeklBYFtnlDwBIhd9OfdMjm4abJfDPccrScqQIPdjFasHxGxV//V7bzfUbEEaEt8N6RT2PI/dC/gvyvf...(过长省略)\",\n    \"sliceNo\": 1\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/upload/v1/oss/file/get_upload_url")
.method("POST", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/upload/v1/oss/file/get_upload_url",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "preuploadID": "h1Kiaaaaaaac/0IDD87IFbIf8T0UWrTNwNNGbGoeklBYFtnlDwBIhd9OfdMjm4abJfDPccrScqQIPdjFasHxGxV//V7bzfUbEEaEt8N6RT2PI/dC/gvyvf...(过长省略)",
    "sliceNo": 1
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "preuploadID": "h1Kiaaaaaaac/0IDD87IFbIf8T0UWrTNwNNGbGoeklBYFtnlDwBIhd9OfdMjm4abJfDPccrScqQIPdjFasHxGxV//V7bzfUbEEaEt8N6RT2PI/dC/gvyvf...(过长省略)",
  "sliceNo": 1
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/upload/v1/oss/file/get_upload_url',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
    "preuploadID": "h1Kiaaaaaaac/0IDD87IFbIf8T0UWrTNwNNGbGoeklBYFtnlDwBIhd9OfdMjm4abJfDPccrScqQIPdjFasHxGxV//V7bzfUbEEaEt8N6RT2PI/dC/gvyvf...(过长省略)",
    "sliceNo": 1
})
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("POST", "/upload/v1/oss/file/get_upload_url", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "presignedURL": "https://m74.123624.com/123-846/e62623f4/1815309870-0/e62623f4906aeba8f8d8f5de19e1e34e?X-Amz-Algorithm=AWS4-HMAC-SHA256...(过长省略)",
    "isMultipart": false
  },
  "x-traceID": "b6ef3daa-f4af-407c-a8ee-4df6e9fec0ea_kong-db-5898fdd8c6-wnv6h"
}
```

## PUT上传分片示例
**请求示例**

```shell
curl --location --request PUT 'https://m74.123624.com/123-846/e62623f4/1815309870-0/e...(过长省略)' \
--header 'Content-Type: application/octet-stream' \
--data-binary '@/C:/Users/<USER>/Downloads/测试图床.jpg'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/octet-stream");
RequestBody body = RequestBody.create(mediaType, "@/C:/Users/<USER>/Downloads/测试图床.jpg");
Request request = new Request.Builder()
.url("https://m74.123624.com/123-846/e62623f4/1815309870-0/e...(过长省略)")
.method("PUT", body)
.addHeader("Content-Type", "application/octet-stream")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://m74.123624.com/123-846/e62623f4/1815309870-0/e...(过长省略)",
  "method": "PUT",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/octet-stream"
  },
  "data": "@/C:/Users/<USER>/Downloads/测试图床.jpg",
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = '@/C:/Users/<USER>/Downloads/测试图床.jpg';

let config = {
  method: 'put',
  maxBodyLength: Infinity,
  url: 'https://m74.123624.com/123-846/e62623f4/1815309870-0/e...(过长省略)',
  headers: { 
    'Content-Type': 'application/octet-stream'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client

conn = http.client.HTTPSConnection("m74.123624.com")
payload = "@/C:/Users/<USER>/Downloads/测试图床.jpg"
headers = {
  'Content-Type': 'application/octet-stream'
}
conn.request("PUT", "/123-846/e62623f4/1815309870-0/e...(过长省略)", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

无响应内容，请求响应200表示分片上传成功



> 更新: 2025-03-17 19:17:28  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/pyfo3a39q6ac0ocd>