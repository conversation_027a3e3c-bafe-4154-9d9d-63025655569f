# 获取用户信息

API： GET 域名 + /api/v1/user/info

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
无

## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| uid | number | 必填 | 用户账号id |
| nickname | string | 必填 | 昵称 |
| headImage | string | 必填 | 头像 |
| passport | string | 必填 | 手机号码 |
| mail | string | 必填 | 邮箱 |
| spaceUsed | number | 必填 | 已用空间 |
| spacePermanent | number | 必填 | 永久空间 |
| spaceTemp | number | 必填 | 临时空间 |
| spaceTempExpr | string | 必填 | 临时空间到期日 |
| vip | bool | 必填 | 是否会员 |
| directTraffic | number | 必填 | 剩余直链流量 |
| isHideUID | bool | 必填 | 直链链接是否隐藏UID |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/api/v1/user/info' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/user/info")
.method("GET", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/user/info",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/user/info',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  }
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = ''
headers = {
  'Content-Type': 'application/json',
  'Platform': 'open_platform',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("GET", "/api/v1/user/info", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "uid": 1815309870,
    "nickname": "18212341234",
    "headImage": "https://download-cdn.cjjd19.com/123-pics/head-pic/1815309456.jpg",
    "passport": "18212341234",
    "mail": "",
    "spaceUsed": ************,
    "spacePermanent": 22849226014720,
    "spaceTemp": 0,
    "spaceTempExpr": 0,
    "vip": true,
    "directTraffic": 9921718327,
    "isHideUID": false
  },
  "x-traceID": "33c7ca8f-2f15-425d-b6b5-7788c62a9907_kong-db-5898fdd8c6-wnv6h"
}
```



> 更新: 2025-03-17 19:16:05  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/fa2w0rosunui2v4m>