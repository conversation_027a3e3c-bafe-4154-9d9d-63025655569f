# 获取图片列表

API： POST 域名 + /api/v1/oss/file/list

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:rgb(0, 0, 0);">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数 
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| parentFileId | string | 选填 | 父级目录Id, 默认为空表示筛选根目录下的文件 |
| limit | number | 必填 | 每页文件数量，最大不超过100 |
| startTime | number | 选填 | 筛选开始时间（时间戳格式，例如 1730390400） |
| endTime | number | 选填 | 筛选结束时间（时间戳格式，例如 1730390400） |
| lastFileId | string | 选填 | 翻页查询时需要填写 |
| type | number | 必填 | 固定为1 |


**返回数据**

| **名称** | | **类型** | **是否必填** | **说明** |
| :---: | --- | :---: | :---: | --- |
|  lastFileId | | string | 必填 | -1代表最后一页（无需再翻页查询）   其他代表下一页开始的文件id，携带到请求参数中 |
| fileList | | array | 必填 | 文件列表 |
|  | fileId | string | 必填 | 文件ID |
|  | filename | string | 必填 | 文件名 |
|  | type | number | 必填 | 0-文件 1-文件 |
|  | size | number | 必填 | 文件大小 |
|  | etag | string | 必填 | md5 |
|  | status | number | 必填 | 文件审核状态。 大于 100 为审核驳回文件 |
|  | createAt | string | 必填 | 创建时间 |
|  | updateAt | string | 必填 | 更新时间 |
|  | downloadURL | string | 必填 | 下载链接 |
|  | userSelfURL | string | 必填 | 自定义域名链接 |
|  | totalTraffic | number | 必填 | 流量统计 |
|  | parentFileId | string | 必填 | 父级ID |
|  | parentFilename | string | 必填 | 父级文件名称 |
|  | extension | string | 必填 | 后缀名称 |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/api/v1/oss/file/list' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "parentFileId": "ymjew503t0m000d5qavemj7c19gu1js0DIYPAIDOBIY0DcxvDwFO",
    "limit": 100,
    "type": 1
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"parentFileId\": \"ymjew503t0m000d5qavemj7c19gu1js0DIYPAIDOBIY0DcxvDwFO\",\n    \"limit\": 100,\n    \"type\": 1\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/oss/file/list")
.method("POST", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/oss/file/list",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "parentFileId": "ymjew503t0m000d5qavemj7c19gu1js0DIYPAIDOBIY0DcxvDwFO",
    "limit": 100,
    "type": 1
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "parentFileId": "ymjew503t0m000d5qavemj7c19gu1js0DIYPAIDOBIY0DcxvDwFO",
  "limit": 100,
  "type": 1
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/oss/file/list',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
    "parentFileId": "ymjew503t0m000d5qavemj7c19gu1js0DIYPAIDOBIY0DcxvDwFO",
    "limit": 100,
    "type": 1
})
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("POST", "/api/v1/oss/file/list", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "lastFileId": "-1",
        "fileList": [
            {
                "fileId": "ymjew503t0l000d7w32x751ex14jo8adDIYPAIDOBIY0DcxvDwFO",
                "filename": "测试图床目录1",
                "parentFileId": "ymjew503t0m000d5qavemj7c19gu1js0DIYPAIDOBIY0DcxvDwFO",
                "type": 1,
                "etag": "",
                "size": 0,
                "status": 0,
                "s3KeyFlag": "",
                "storageNode": "",
                "createAt": "2025-03-03 15:43:46",
                "updateAt": "2025-03-03 15:43:46",
                "downloadURL": "",
                "ossIndex": 42,
                "totalTraffic": 0,
                "parentFilename": "img_oss",
                "extension": "",
                "userSelfURL": ""
            },
            {
                "fileId": "yk6baz03t0l000d7w33fbyq51l4izkneDIYPAIDOBIY0DcxvDwFO",
                "filename": "测试图床目录",
                "parentFileId": "ymjew503t0m000d5qavemj7c19gu1js0DIYPAIDOBIY0DcxvDwFO",
                "type": 1,
                "etag": "",
                "size": 0,
                "status": 0,
                "s3KeyFlag": "",
                "storageNode": "",
                "createAt": "2025-03-03 15:07:54",
                "updateAt": "2025-03-03 15:07:54",
                "downloadURL": "",
                "ossIndex": 42,
                "totalTraffic": 0,
                "parentFilename": "img_oss",
                "extension": "",
                "userSelfURL": ""
            }
        ]
    },
    "x-traceID": "5e0aa7b9-b430-41d0-bca0-647e28f47ec6_kong-db-5898fdd8c6-wnv6h"
}
```



> 更新: 2025-03-17 19:17:37  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/zayr72q8xd7gg4f8>