# 创建分享链接

API： POST 域名 + /api/v1/share/create

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| shareName | string | <font style="color:#000000;">必填</font> | 分享链接 |
| shareExpire | number | 必填 | 分享链接有效期天数,该值为枚举<br/>固定只能填写:1、7、30、0<br/>填写0时代表永久分享 |
| fileIDList | string | 必填 | 分享文件ID列表,以逗号分割,最大只支持拼接100个文件ID,示例:1,2,3 |
| sharePwd | string | 选填 | 设置分享链接提取码 |
| trafficSwitch   | int | 选填 | 免登录流量包开关   1 关闭免登录流量包<br/>2 打开免登录流量包 |
| trafficLimitSwitch | int | 选填 | 免登录流量限制开关<br/>1 关闭限制<br/>2 打开限制 |
| trafficLimit   | int64 | 选填 | 免登陆限制流量<br/>单位：字节 |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| shareID | number | 必填 | 分享ID |
| shareKey | string | 必填 | 分享码,请将分享码拼接至 https://www.123pan.com/s/ 后面访问,即是分享页面 |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/api/v1/share/create' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "shareName": "测试分享链接",
    "shareExpire": 1,
    "fileIDList": "14713526,10861131"
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"shareName\": \"测试分享链接\",\n    \"shareExpire\": 1,\n    \"fileIDList\": \"14713526,10861131\"\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/share/create")
.method("POST", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/share/create",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "shareName": "测试分享链接",
    "shareExpire": 1,
    "fileIDList": "14713526,10861131"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "shareName": "测试分享链接",
  "shareExpire": 1,
  "fileIDList": "14713526,10861131"
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/share/create',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
    "shareName": "测试分享链接",
    "shareExpire": 1,
    "fileIDList": "14713526,10861131"
})
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("POST", "/api/v1/share/create", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "shareID": 87187530,
    "shareKey": "PvitVv-nPeLH"
  },
  "x-traceID": "1e218fcd-dd28-48d3-96a8-d3ab090551d4_kong-db-5898fdd8c6-wgsts"
}
```



> 更新: 2025-03-17 19:16:22  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/dwd2ss0qnpab5i5s>