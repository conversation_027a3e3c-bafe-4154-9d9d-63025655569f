# 查询某个视频的转码记录

API： POST 域名 + /api/v1/transcode/video/record

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| fileId | number | 必填 | 文件Id |


### body参数示例
```json
{
  "fileId": 2875008
}
```

## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| UserTranscodeVideoRecordList | array | 必填 | 用户转码记录列表 |
| create_at | string | 必填 | 创建时间 |
| resolution | string | 必填 | 分辨率 |
| status | number | 必填 | 1：准备转码   2：正在转码中    3-254：转码失败，时长会自动回退 255：转码成功 |
| link | string | 选填 | 视频转码成功之后的m3u8的链接 |


### **返回示例**
#### 正在转码中或转码失败
```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "UserTranscodeVideoRecordList": [
            {
                "create_at": "2024-12-19 13:19:33",
                "resolution": "720P",
                "status": 1,
                "link": ""
            },
            {
                "create_at": "2024-12-19 13:19:33",
                "resolution": "1080P",
                "status": 1,
                "link": ""
            }
        ]
    },
    "x-traceID": "63e21238-5f6f-46a8-a9f3-d3abc0b03544_test-kong-5bd74855d7-c2t4z"
}
```

#### 转码成功（状态为255）
```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "UserTranscodeVideoRecordList": [
            {
                "create_at": "2024-12-19 13:19:33",
                "resolution": "720P",
                "status": 255,
                "link": "http://vipdev.123pan.com/1814435971/transcode/good.m3u8?extParams=m3u8&resolutions=720p&suffix=mp4&from=transcode"
            },
            {
                "create_at": "2024-12-19 13:19:33",
                "resolution": "1080P",
                "status": 255,
                "link": "http://vipdev.123pan.com/1814435971/transcode/good.m3u8?extParams=m3u8&resolutions=1080p&suffix=mp4&from=transcode"
            }
        ]
    },
    "x-traceID": ""
}
```



> 更新: 2025-03-17 19:16:40  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/ost1m82sa9chh0mc>