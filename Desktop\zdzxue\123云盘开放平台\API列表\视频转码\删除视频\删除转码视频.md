# 删除转码视频

API： POST 域名 + /api/v1/transcode/delete

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| fileId | number | 必填 | 文件Id |
| businessType | number | 必填 | businessType只能是2 |
| trashed | number | 必填 | 1：删除原文件<br/>2：删除原文件+转码后的文件 |


### body参数示例
```json
{
    "fileId": 2875061,
    "businessType": 2,
    "trashed": 2
}
```

## 返回数据
```json
{
    "code": 0,
    "message": "ok",
    "data": "删除文件成功",
    "x-traceID": "3cda733a-585d-41c3-b180-0e2cf708f0bf_test-kong-5bd74855d7-c2t4z"
}
```



> 更新: 2025-03-17 19:16:49  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/tg2xgotkgmgpulrp>