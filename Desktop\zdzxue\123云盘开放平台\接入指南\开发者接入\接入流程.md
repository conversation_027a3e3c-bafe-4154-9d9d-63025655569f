# 接入流程

> 此接入流程仅适用于API接口的调用链接
>

# 1. 申请账号
请您访问[123开放平台官网](https://www.123pan.com/developer)，阅读开发者协议，填写对应必填项`*`信息，申请`client_id`和`client_secret`。申请通过后会自动发送到您填入的邮箱地址，请您妥善保存！！！

# 2. <font style="color:rgb(55, 60, 67);">获取访问凭证</font>
访问凭证（也称为 `**<font style="background-color:rgb(246, 247, 249);">access_token</font>**`）。调用 API 时，需要在 HTTP Header 中携带访问凭证。有关访问凭证的介绍详见 [ 获取access_token](https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/gn1nai4x0v0ry9ki)

# 3. 调用 API
<font style="color:rgb(55, 60, 67);">完成上述步骤后，就可以调用 API 了</font>



> 更新: 2025-03-17 19:18:39  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/hpengmyg32blkbg8>