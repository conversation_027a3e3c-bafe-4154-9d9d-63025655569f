/* 校评页面专用样式 */

/* 页面主体样式 */
.review-main {
    margin-top: 80px; /* 为固定导航栏留出空间 */
    min-height: calc(100vh - 80px);
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--secondary-color) 100%);
}

/* 页面标题区域 */
.page-header {
    padding: 60px 0 40px;
    text-align: center;
    background: linear-gradient(135deg, var(--primary-color), var(--light-purple));
    color: var(--secondary-color);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* 搜索和排序区域 */
.search-sort-section {
    padding: 30px 0;
    background: var(--secondary-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-sort-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
}

.search-box {
    display: flex;
    align-items: center;
    background: var(--light-gray);
    border-radius: 25px;
    padding: 5px;
    flex: 1;
    max-width: 500px;
    position: relative;
}

.search-icon {
    color: var(--primary-color);
    margin: 0 15px;
    font-size: 1.1rem;
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 12px 10px;
    font-size: 1rem;
    outline: none;
    color: var(--text-color);
}

.search-input::placeholder {
    color: #999;
}

.search-btn {
    background: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    padding: 12px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: var(--hover-color);
    transform: translateY(-2px);
}

.sort-box {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-label {
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
}

.sort-select {
    padding: 10px 15px;
    border: 2px solid var(--accent-color);
    border-radius: 8px;
    background: var(--secondary-color);
    color: var(--text-color);
    font-size: 1rem;
    cursor: pointer;
    outline: none;
    transition: all 0.3s ease;
}

.sort-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(94, 53, 177, 0.1);
}

/* 添加评论区域 */
.add-comment-section {
    padding: 40px 0;
}

.add-comment-container {
    max-width: 800px;
    margin: 0 auto;
}

.add-comment-box {
    background: var(--secondary-color);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 30px rgba(94, 53, 177, 0.15);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px dashed var(--accent-color);
}

.add-comment-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(94, 53, 177, 0.25);
    border-color: var(--primary-color);
}

.add-comment-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    color: var(--primary-color);
}

.add-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.add-text {
    font-size: 1.3rem;
    font-weight: 600;
}

/* 评论列表区域 */
.comments-section {
    padding: 0 0 60px;
}

.comments-container {
    max-width: 800px;
    margin: 0 auto;
}

/* 加载指示器 */
.loading-indicator {
    text-align: center;
    padding: 40px;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.loading-indicator i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

/* 评论卡片样式 */
.comment-card {
    background: var(--secondary-color);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.comment-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

/* 评论卡片颜色边框 */
.comment-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: var(--primary-color);
    transition: all 0.3s ease;
}

.comment-card:hover::before {
    width: 8px;
}

/* 评论头部 */
.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.comment-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.comment-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--accent-color);
}

.comment-user-details h4 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-color);
    font-weight: 600;
}

.comment-time {
    font-size: 0.9rem;
    color: #666;
    margin-top: 2px;
}

.comment-actions {
    display: flex;
    gap: 10px;
}

.action-btn-small {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.action-btn-small:hover {
    background: var(--accent-color);
    color: var(--primary-color);
}

/* 评论内容 */
.comment-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 10px;
    line-height: 1.4;
}

.comment-text {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
    margin-bottom: 15px;
}

/* 评论图片 */
.comment-images {
    margin-bottom: 15px;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.comment-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.comment-image:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 评论底部操作 */
.comment-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid var(--accent-color);
}

.comment-stats {
    display: flex;
    gap: 20px;
}

.stat-btn {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.stat-btn:hover {
    background: var(--accent-color);
    color: var(--primary-color);
}

.stat-btn.liked {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.stat-btn.disliked {
    color: #95a5a6;
    background: rgba(149, 165, 166, 0.1);
}

.comment-reply-btn {
    background: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.comment-reply-btn:hover {
    background: var(--hover-color);
    transform: translateY(-2px);
}

/* 回复列表 */
.replies-container {
    margin-top: 20px;
    padding-left: 20px;
    border-left: 3px solid var(--accent-color);
}

.reply-item {
    background: var(--light-gray);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    position: relative;
}

.reply-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.reply-user {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.95rem;
}

.reply-time {
    font-size: 0.85rem;
    color: #666;
}

.reply-content {
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--text-color);
}

/* 加载更多按钮 */
.load-more-container {
    text-align: center;
    padding: 30px 0;
}

.load-more-btn {
    background: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.load-more-btn:hover {
    background: var(--hover-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(94, 53, 177, 0.3);
}

/* 弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.comment-modal,
.reply-modal {
    background: var(--secondary-color);
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(-50px);
    transition: all 0.3s ease;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-overlay.active .comment-modal,
.modal-overlay.active .reply-modal {
    transform: translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 2px solid var(--accent-color);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: var(--accent-color);
    color: var(--primary-color);
}

.modal-body {
    padding: 30px;
}

/* 表单样式 */
.comment-form,
.reply-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    position: relative;
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
    font-size: 1rem;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid var(--accent-color);
    border-radius: 10px;
    font-size: 1rem;
    font-family: inherit;
    outline: none;
    transition: all 0.3s ease;
    resize: vertical;
}

.form-input:focus,
.form-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(94, 53, 177, 0.1);
}

.form-textarea {
    min-height: 120px;
    line-height: 1.6;
}

.char-count {
    position: absolute;
    bottom: -20px;
    right: 0;
    font-size: 0.85rem;
    color: #666;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--accent-color);
    color: var(--primary-color);
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-2px);
}

/* 颜色选择器 */
.color-picker-container {
    margin-top: 15px;
    padding: 20px;
    background: var(--light-gray);
    border-radius: 10px;
}

/* 图片预览 */
.image-preview-container {
    margin-top: 20px;
}

.preview-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 15px;
}

.image-preview-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
}

.image-preview-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background: var(--light-gray);
}

.preview-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    display: block;
}

.remove-image-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(231, 76, 60, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.remove-image-btn:hover {
    background: #e74c3c;
    transform: scale(1.1);
}

/* 提交按钮区域 */
.form-submit {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid var(--accent-color);
}

.cancel-btn {
    background: #95a5a6;
    color: var(--secondary-color);
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.cancel-btn:hover {
    background: #7f8c8d;
    transform: translateY(-2px);
}

.submit-btn {
    background: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    background: var(--hover-color);
    transform: translateY(-2px);
}

.submit-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

/* 回复信息显示 */
.reply-to-info {
    background: var(--light-gray);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid var(--primary-color);
}

.reply-to-info h4 {
    margin: 0 0 5px 0;
    color: var(--primary-color);
    font-size: 1rem;
}

.reply-to-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .search-sort-container {
        flex-direction: column;
        gap: 20px;
    }
    
    .search-box {
        max-width: 100%;
    }
    
    .sort-box {
        width: 100%;
        justify-content: space-between;
    }
    
    .comment-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .comment-footer {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .comment-stats {
        width: 100%;
        justify-content: space-around;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .form-submit {
        flex-direction: column;
    }
    
    .image-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 40px 0 30px;
    }
    
    .page-title {
        font-size: 1.8rem;
    }
    
    .add-comment-box {
        padding: 20px;
    }
    
    .add-text {
        font-size: 1.1rem;
    }
    
    .comment-card {
        padding: 20px;
    }
    
    .modal-header {
        padding: 20px;
    }
    
    .modal-title {
        font-size: 1.3rem;
    }
}

/* 导航栏活跃状态 */
.nav-link.active {
    background-color: var(--secondary-color);
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.comment-card {
    animation: fadeInUp 0.6s ease-out;
}

/* 滚动条样式 */
.modal-overlay::-webkit-scrollbar,
.comment-modal::-webkit-scrollbar,
.reply-modal::-webkit-scrollbar {
    width: 8px;
}

.modal-overlay::-webkit-scrollbar-track,
.comment-modal::-webkit-scrollbar-track,
.reply-modal::-webkit-scrollbar-track {
    background: var(--light-gray);
    border-radius: 4px;
}

.modal-overlay::-webkit-scrollbar-thumb,
.comment-modal::-webkit-scrollbar-thumb,
.reply-modal::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

.modal-overlay::-webkit-scrollbar-thumb:hover,
.comment-modal::-webkit-scrollbar-thumb:hover,
.reply-modal::-webkit-scrollbar-thumb:hover {
    background: var(--hover-color);
}
