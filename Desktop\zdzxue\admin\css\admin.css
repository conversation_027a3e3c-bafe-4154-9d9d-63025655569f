/* 后台管理页面专用样式 */

/* 主要内容区域 */
.admin-main {
    margin-top: 80px; /* 为固定导航栏留出空间 */
    min-height: calc(100vh - 80px);
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--secondary-color) 100%);
}

/* 页面标题区域 */
.admin-header {
    padding: 60px 0 40px;
    text-align: center;
    background: linear-gradient(135deg, var(--primary-color), var(--light-purple));
    color: var(--secondary-color);
    position: relative;
    overflow: hidden;
}

.admin-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.admin-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.admin-title i {
    margin-right: 15px;
    color: rgba(255, 255, 255, 0.9);
}

.admin-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* 管理内容区域 */
.admin-content {
    padding: 40px 0;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: var(--secondary-color);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(94, 53, 177, 0.1);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--light-purple));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    color: var(--secondary-color);
    font-size: 1.5rem;
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0 0 5px 0;
}

.stat-info p {
    color: #666;
    margin: 0;
    font-size: 0.9rem;
}

/* 管理区域 */
.management-section {
    background: var(--secondary-color);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(94, 53, 177, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--accent-color);
}

.section-header h2 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin: 0;
}

.section-header h2 i {
    margin-right: 10px;
}

/* 搜索容器 */
.search-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input {
    padding: 10px 15px;
    border: 2px solid var(--accent-color);
    border-radius: 25px;
    font-size: 0.9rem;
    width: 250px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(94, 53, 177, 0.1);
}

.search-btn {
    padding: 10px 15px;
    background: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: var(--hover-color);
    transform: scale(1.05);
}

/* 用户列表容器 */
.user-list-container {
    border: 1px solid var(--accent-color);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
}

.user-list-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 2fr;
    background: var(--accent-color);
    padding: 15px;
    font-weight: bold;
    color: var(--primary-color);
}

.header-cell {
    padding: 0 10px;
    display: flex;
    align-items: center;
}

.user-list {
    max-height: 600px;
    overflow-y: auto;
}

.user-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 2fr;
    padding: 15px;
    border-bottom: 1px solid var(--accent-color);
    transition: background-color 0.3s ease;
}

.user-item:hover {
    background-color: rgba(94, 53, 177, 0.05);
}

.user-item:last-child {
    border-bottom: none;
}

.user-cell {
    padding: 0 10px;
    display: flex;
    align-items: center;
}

/* 用户信息 */
.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-avatar-small {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--accent-color);
}

.user-details h4 {
    margin: 0 0 5px 0;
    color: var(--primary-color);
    font-size: 1rem;
}

.user-details p {
    margin: 0;
    color: #666;
    font-size: 0.8rem;
}

/* 角色标签 */
.role-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-align: center;
}

.role-admin {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.role-user {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

/* 用户头衔 */
.user-title {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    color: white;
    text-align: center;
    display: inline-block;
    min-width: 60px;
}

.no-title {
    color: #999;
    font-style: italic;
}

/* 状态标签 */
.status-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-align: center;
}

.status-active {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.status-banned {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-title {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.btn-title:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
}

.btn-ban {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-ban:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
}

.btn-unban {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.btn-unban:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
}

/* 分页控制 */
.pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
}

.pagination-btn {
    padding: 10px 20px;
    background: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--hover-color);
    transform: translateY(-2px);
}

.pagination-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.page-info {
    color: var(--primary-color);
    font-weight: bold;
}

/* 弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--secondary-color);
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    transform: translateY(-20px);
    transition: all 0.4s ease;
}

.modal-overlay.active .modal-content {
    transform: translateY(0);
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 2px solid var(--accent-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.3rem;
}

.modal-header h3 i {
    margin-right: 10px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #999;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: var(--accent-color);
    color: var(--primary-color);
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 2px solid var(--accent-color);
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--primary-color);
    font-weight: bold;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--accent-color);
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(94, 53, 177, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-hint {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 0.8rem;
}

/* 颜色选择器 */
.color-picker-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.color-picker {
    width: 50px;
    height: 40px;
    border: 2px solid var(--accent-color);
    border-radius: 8px;
    cursor: pointer;
}

.color-presets {
    display: flex;
    gap: 8px;
}

.color-preset {
    width: 30px;
    height: 30px;
    border: 2px solid #ddd;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.color-preset:hover {
    transform: scale(1.1);
    border-color: var(--primary-color);
}

/* 头衔预览 */
.title-preview {
    padding: 15px;
    background: var(--accent-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

#previewUsername {
    font-weight: bold;
    color: var(--primary-color);
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--light-purple));
    color: var(--secondary-color);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(94, 53, 177, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: var(--secondary-color);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: var(--secondary-color);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

/* 通知样式 */
.notification {
    padding: 12px 15px;
    border-radius: 8px;
    margin-top: 15px;
    font-weight: bold;
    opacity: 0;
    transition: all 0.3s ease;
}

.notification.success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 1px solid #c3e6cb;
    opacity: 1;
}

.notification.error {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border: 1px solid #f5c6cb;
    opacity: 1;
}

.notification.info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
    border: 1px solid #bee5eb;
    opacity: 1;
}

/* 导航栏活跃状态 */
.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

/* 个人信息弹窗样式 */
.profile-modal {
    background: var(--secondary-color);
    border-radius: 15px;
    width: 90%;
    max-width: 450px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    transform: translateY(-20px);
    transition: all 0.4s ease;
}

.profile-content {
    padding: 25px;
}

.avatar-section {
    text-align: center;
    margin-bottom: 25px;
}

.current-avatar {
    margin-bottom: 15px;
}

.current-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color);
}

.avatar-upload-label {
    display: inline-block;
    background: var(--primary-color);
    color: var(--secondary-color);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.avatar-upload-label:hover {
    background: var(--hover-color);
    transform: translateY(-2px);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--accent-color);
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(94, 53, 177, 0.1);
}

.user-id-section {
    background: var(--accent-color);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.user-id-section p {
    margin: 0;
    color: var(--primary-color);
}

.id-note {
    font-size: 0.8rem;
    color: #666 !important;
    margin-top: 5px !important;
}

.profile-buttons {
    display: flex;
    gap: 15px;
    justify-content: space-between;
}

.submit-btn {
    flex: 1;
    background: linear-gradient(135deg, var(--primary-color), var(--light-purple));
    color: var(--secondary-color);
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(94, 53, 177, 0.3);
}

.logout-btn {
    flex: 1;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: var(--secondary-color);
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }

    .search-container {
        justify-content: center;
    }

    .search-input {
        width: 100%;
    }

    .user-list-header,
    .user-item {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .user-cell {
        padding: 5px 10px;
    }

    .action-buttons {
        justify-content: center;
    }

    .pagination-container {
        flex-direction: column;
        gap: 10px;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }
}
