# 移动图片

API： POST 域名 + /api/v1/oss/file/move

说明：批量移动文件，单级最多支持100个

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| fileIDs | array | 必填 | 文件id数组 |
| toParentFileID | string | 必填 | 要移动到的目标文件夹id，移动到根目录时填写 空 |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/api/v1/oss/file/move' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "fileIDs": [
        "yk6baz03t0l000d7w33fbyt8704m2bohDIYPAIDOBIY0DcxvDwFO"
    ],
    "toParentFileID": "ymjew503t0l000d7w32x751ex14jo8adDIYPAIDOBIY0DcxvDwFO" 
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"fileIDs\": [\n        \"yk6baz03t0l000d7w33fbyt8704m2bohDIYPAIDOBIY0DcxvDwFO\"\n    ],\n    \"toParentFileID\": \"ymjew503t0l000d7w32x751ex14jo8adDIYPAIDOBIY0DcxvDwFO\" \n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/oss/file/move")
.method("POST", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/oss/file/move",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "fileIDs": [
      "yk6baz03t0l000d7w33fbyt8704m2bohDIYPAIDOBIY0DcxvDwFO"
    ],
    "toParentFileID": "ymjew503t0l000d7w32x751ex14jo8adDIYPAIDOBIY0DcxvDwFO"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "fileIDs": [
    "yk6baz03t0l000d7w33fbyt8704m2bohDIYPAIDOBIY0DcxvDwFO"
  ],
  "toParentFileID": "ymjew503t0l000d7w32x751ex14jo8adDIYPAIDOBIY0DcxvDwFO"
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/oss/file/move',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
  "fileIDs": [
    "yk6baz03t0l000d7w33fbyt8704m2bohDIYPAIDOBIY0DcxvDwFO"
  ],
  "toParentFileID": "ymjew503t0l000d7w32x751ex14jo8adDIYPAIDOBIY0DcxvDwFO"
})
headers = {
  'Content-Type': 'application/json',
  'Platform': 'open_platform',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("POST", "/api/v1/oss/file/move", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
    "code": 0,
    "message": "ok",
    "data": null,
    "x-traceID": "a6fd87a0-b963-44d5-bdfd-e3c30e22c83d_kong-db-5898fdd8c6-t5pvc"
}
```



> 更新: 2025-03-17 19:17:32  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/eqeargimuvycddna>