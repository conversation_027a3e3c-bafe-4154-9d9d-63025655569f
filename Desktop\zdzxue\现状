你现在只需记住以下内容,不要干其他任何事，不要阅读需求和需求.txt
这是一个学校的网站项目
我将上传到github并通过vercel进行部署，请注意符合vercel要求
1.颜色：主要以紫色与白色为主色，搭配浅灰或低饱和度的辅助色（如浅蓝、米白）平衡视觉冲击力
2.风格：简洁，美观；采用视差滚动效果，滑动时图片与文字分层移动，增强沉浸感；
3.设计语言：JavaScript,css,html等简单语言结构，不要使用复杂框架
4.文件布局：
网站图片统一放到images文件夹中，文件按照网页功能单独放在一个文件夹中
5.数据储存:图片使用123图床存储，注意分类，创建新的文件夹。客户信息等其他数据，用visa的kv数据库储存所有apikey单等都通过vercel的环境变量来调用，单独存放在.env文件中
6.请保持所有页面导航栏功能一致
我已完成主页开发，请根据需求继续开发
C:\Users\<USER>\Desktop\zdzxue\123云盘开放平台\接入指南\开发者接入
C:\Users\<USER>\Desktop\zdzxue\123云盘开放平台\API列表\图床\复制云盘图片
C:\Users\<USER>\Desktop\zdzxue\123云盘开放平台\API列表\图床\获取图片信息
C:\Users\<USER>\Desktop\zdzxue\123云盘开放平台\API列表\图床\上传图片
C:\Users\<USER>\Desktop\zdzxue\123云盘开放平台\API列表\图床\图床离线迁移
C:\Users\<USER>\Desktop\zdzxue\123云盘开放平台\API列表\图床

