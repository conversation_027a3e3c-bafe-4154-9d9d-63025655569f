# 获取转码空间文件列表

API： GET 域名 + /api/v2/file/list

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## QueryString 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| parentFileId | number | 必填 | 文件夹ID，根目录传 0 |
| limit | number | 必填 | 每页文件数量，最大不超过100 |
| searchData | string | 选填 | 搜索关键字将无视文件夹ID参数。将会进行全局查找 |
| searchMode | number | 选填 | 0:全文模糊搜索(注:将会根据搜索项分词,查找出相似的匹配项)<br/>1:精准搜索(注:精准搜索需要提供完整的文件名) |
| lastFileId | number | 选填 | 翻页查询时需要填写 |
| <font style="color:#DF2A3F;">businessType</font> | <font style="color:#DF2A3F;">number</font> | <font style="color:#DF2A3F;">必填</font> | <font style="color:#DF2A3F;">固定为2，2代表转码空间</font> |


## 返回数据
| **名称** | | **类型** | **是否必填** | **说明** |
| --- | --- | :---: | :---: | --- |
|  lastFileId<br/> | | number | 必填 | -1代表最后一页（无需再翻页查询）<br/>其他代表下一页开始的文件id，携带到请求参数中 |
| fileList | | array | 必填 | 文件列表 |
|  | fileId | number | 必填 | 文件Id |
|  | filename | string | 必填 | 文件名 |
|  | type | number | 必填 | 0-文件  1-文件夹 |
|  | size | number | 必填 | 文件大小 |
|  | etag | string | 必填 | md5 |
|  | status | number | 必填 | 文件审核状态。 大于 100 为审核驳回文件 |
|  | parentFileId | number | 必填 | 目录ID |
|  | category | number | 必填 | 文件分类：0-未知 1-音频 2-视频 3-图片 |
|  | trashed | number | 必填 | 是否在回收站：0-否 1-是 |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/api/v2/file/list?parentFileId=0&limit=100&businessType=2' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--header 'Platform: open_platform' \
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("text/plain");
RequestBody body = RequestBody.create(mediaType, "");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v2/file/list?parentFileId=0&limit=100&businessType=2")
.method("GET", body)
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.addHeader("Platform", "open_platform")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v2/file/list?parentFileId=0&limit=100&businessType=2",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)",
    "Platform": "open_platform"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v2/file/list?parentFileId=0&limit=100&businessType=2',
  headers: { 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)', 
    'Platform': 'open_platform'
  }
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = ''
headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)',
  'Platform': 'open_platform'
}
conn.request("GET", "/api/v2/file/list?parentFileId=0&limit=100&businessType=2", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "lastFileId": -1,
        "fileList": [
            {
                "fileId": 13210718,
                "filename": "transcode",
                "parentFileId": 0,
                "type": 1,
                "etag": "",
                "size": 0,
                "category": 0,
                "status": 0,
                "punishFlag": 0,
                "s3KeyFlag": "1815309870-0",
                "storageNode": "m0",
                "trashed": 0,
                "createAt": "2025-01-08 14:07:37",
                "updateAt": "2025-01-08 14:07:37"
            }
        ]
    },
    "x-traceID": "97b5bb4b-ebfb-496c-b7bb-7383e8f58fe2_kong-db-5898fdd8c6-wnv6h"
}
```



> 更新: 2025-03-17 19:16:32  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/ux9wct58lvllxm1n>