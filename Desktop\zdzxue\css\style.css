/* 全局样式 */
:root {
    /* 主色调 */
    --primary-color: #5e35b1; /* 更柔和的紫色，更适合与logo搭配 */
    --secondary-color: #ffffff; /* 白色 */
    --accent-color: #e8eaf6; /* 浅紫蓝色 */
    --light-gray: #f5f5f5; /* 浅灰色 */
    --text-color: #333333; /* 文字颜色 */
    --light-purple: #7e57c2; /* 浅紫色 */
    --hover-color: #4527a0; /* 悬停时的深紫色 */
    --nav-gradient-start: #5e35b1; /* 导航栏渐变起始色 */
    --nav-gradient-end: #3949ab; /* 导航栏渐变结束色 */
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--secondary-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏样式 */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 50px;
    /* 使用渐变背景，更加美观 */
    background: linear-gradient(135deg, var(--nav-gradient-start), var(--nav-gradient-end));
    color: var(--secondary-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    /* 添加微妙的边框底部，增强视觉效果 */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-container {
    display: flex;
    align-items: center;
    /* 添加过渡效果 */
    transition: transform 0.3s ease;
}

/* 鼠标悬停时轻微放大效果 */
.logo-container:hover {
    transform: scale(1.02);
}

.logo {
    height: 50px;
    margin-right: 15px;
    /* 添加轻微阴影效果，使logo更加突出 */
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    border-radius: 50%;
    /* 添加边框 */
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.school-name {
    font-size: 1.5rem;
    font-weight: bold;
    /* 添加文字阴影，增强可读性 */
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    /* 添加字间距，使文字更加优雅 */
    letter-spacing: 0.5px;
}

.nav-links {
    display: flex;
    gap: 25px;
    /* 调整导航链接区域的宽度，使其在只有一个链接时仍然占据适当空间 */
    flex: 1;
    justify-content: center;
}

.nav-link {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 600; /* 加粗字体 */
    /* 添加多个过渡效果 */
    transition: all 0.3s ease;
    padding: 10px 20px; /* 增加内边距 */
    border-radius: 30px; /* 圆角按钮效果 */
    position: relative;
    font-size: 1.1rem; /* 增大字体 */
    letter-spacing: 1px; /* 增加字间距 */
    background-color: rgba(255, 255, 255, 0.1); /* 添加轻微背景 */
    border: 1px solid rgba(255, 255, 255, 0.2); /* 添加边框 */
}

/* 添加悬停效果 */
.nav-link:hover {
    color: var(--primary-color);
    background-color: var(--secondary-color);
    transform: translateY(-3px); /* 轻微上浮效果 */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); /* 添加阴影 */
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    /* 添加更精致的边框 */
    border: 2px solid rgba(255, 255, 255, 0.8);
    /* 添加过渡效果 */
    transition: all 0.3s ease;
    /* 添加阴影效果 */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 鼠标悬停时的效果 */
.user-avatar img:hover {
    transform: scale(1.05);
    border-color: var(--secondary-color);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.login-btn {
    /* 使用渐变背景 */
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--secondary-color);
    padding: 8px 18px;
    border-radius: 30px; /* 圆角按钮 */
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    /* 添加文字阴影 */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    /* 添加按钮阴影 */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.login-btn:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
    /* 悬停时增强阴影效果 */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    /* 轻微放大效果 */
    transform: translateY(-2px);
}

/* 欢迎语部分 */
.welcome-section {
    padding: 120px 0 40px;
    /* 使用渐变背景，与导航栏形成呼应 */
    background: linear-gradient(to bottom, var(--accent-color), rgba(232, 234, 246, 0.7));
    text-align: center;
    /* 添加微妙的阴影效果 */
    box-shadow: inset 0 -5px 10px rgba(0, 0, 0, 0.03);
}

.welcome-text {
    font-size: 2.2rem;
    font-weight: bold;
    color: var(--primary-color);
    min-height: 60px;
    /* 添加文字阴影 */
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    /* 添加过渡效果 */
    transition: all 0.5s ease;
    /* 添加字间距 */
    letter-spacing: 0.5px;
}

/* 学校简介部分 */
.intro-section {
    padding: 60px 0;
    background-color: var(--secondary-color);
    /* 添加背景图案，增强视差效果 */
    background-image: url('../images/pattern.png');
    background-size: 200px;
    background-repeat: repeat;
    background-attachment: fixed;
    background-blend-mode: overlay;
    position: relative;
    overflow: hidden;
}

.section-title {
    text-align: center;
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 30px;
    position: relative;
}

.section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
    margin: 10px auto 0;
}

.intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: justify;
}

/* 新增校园全景图样式 */
.campus-panorama {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(94, 53, 177, 0.2);
    margin-bottom: 30px;
    transition: transform 0.3s ease;
}

/* 添加悬停动画 */
.campus-panorama:hover {
    transform: scale(1.02);
}

/* 更新简介段落样式 */
.intro-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 25px;
    padding: 15px;
    background: rgba(232, 234, 246, 0.3);
    border-radius: 8px;
}

/* 主轮播图样式 */
.main-carousel {
    padding: 40px 0;
    /* 使用渐变背景 */
    background: linear-gradient(to bottom, var(--light-gray), var(--secondary-color));
    position: relative;
    overflow: hidden;
    /* 添加视差效果所需的属性 */
    background-attachment: fixed;
}

.carousel-container {
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    /* 添加过渡效果 */
    transition: transform 0.3s ease;
}

/* 鼠标悬停时轻微放大效果 */
.carousel-container:hover {
    transform: scale(1.01);
}

.carousel-slides {
    position: relative;
    height: 450px;
    overflow: hidden;
    /* 更现代的圆角 */
    border-radius: 15px;
    /* 更精致的阴影效果 */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15), 0 5px 15px rgba(106, 27, 154, 0.1);
    /* 添加边框 */
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.carousel-slide.active {
    opacity: 1;
}

.carousel-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(106, 27, 154, 0.7);
    color: white;
    padding: 15px;
    font-size: 1.2rem;
    text-align: center;
}

.carousel-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    padding: 0 20px;
}

.prev-btn, .next-btn {
    background-color: rgba(255, 255, 255, 0.7);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--primary-color);
    transition: background-color 0.3s;
}

.prev-btn:hover, .next-btn:hover {
    background-color: rgba(255, 255, 255, 0.9);
}

.carousel-indicators {
    display: flex;
    gap: 10px;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: background-color 0.3s;
}

.indicator.active {
    background-color: white;
}

/* 新闻动态部分 */
.news-section {
    padding: 60px 0;
    background-color: var(--secondary-color);
}

.news-filter {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
}

.filter-btn {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
}

.filter-btn.active, .filter-btn:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
}

.news-card {
    background-color: var(--light-gray);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s, box-shadow 0.3s;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.news-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.news-content {
    padding: 20px;
}

.news-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.news-date {
    color: #777;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.news-excerpt {
    margin-bottom: 15px;
    color: var(--text-color);
}

.read-more {
    display: inline-block;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s;
}

.read-more:hover {
    color: var(--hover-color);
}

/* 登录弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.login-modal {
    background: linear-gradient(135deg, #ffffff, #f5f5f5);
    border-radius: 15px;
    width: 90%;
    max-width: 400px;
    padding: 30px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    position: relative;
    transform: translateY(-20px);
    transition: all 0.4s ease;
    border: 1px solid rgba(94, 53, 177, 0.1);
}

.modal-overlay.active .login-modal {
    transform: translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 2px solid rgba(94, 53, 177, 0.1);
    padding-bottom: 15px;
}

.modal-title {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #999;
    cursor: pointer;
    transition: color 0.3s;
}

.close-modal:hover {
    color: var(--primary-color);
}

.modal-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.modal-tab {
    padding: 10px 20px;
    background: none;
    border: none;
    color: #777;
    font-weight: 500;
    cursor: pointer;
    position: relative;
    transition: all 0.3s;
}

.modal-tab.active {
    color: var(--primary-color);
}

.modal-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(94, 53, 177, 0.1);
    outline: none;
}

.form-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 25px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-right: 8px;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s;
}

.forgot-password:hover {
    text-decoration: underline;
}

.submit-btn {
    background: linear-gradient(135deg, var(--nav-gradient-start), var(--nav-gradient-end));
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 30px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    margin-top: 15px;
    width: 100%;
    box-shadow: 0 4px 10px rgba(94, 53, 177, 0.2);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(94, 53, 177, 0.3);
}

.register-prompt {
    text-align: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    color: #777;
}

.register-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s;
}

.register-link:hover {
    text-decoration: underline;
}

.form-tab {
    display: none;
}

.form-tab.active {
    display: block;
}

.notification {
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 0.9rem;
    display: none;
}

.notification.error {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
    display: block;
}

.notification.success {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
    display: block;
}

/* 个人信息弹窗样式 */
.profile-modal {
    background: linear-gradient(135deg, #ffffff, #f5f5f5);
    border-radius: 15px;
    width: 90%;
    max-width: 450px;
    padding: 30px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    position: relative;
    transform: translateY(-20px);
    transition: all 0.4s ease;
    border: 1px solid rgba(94, 53, 177, 0.1);
}

.modal-overlay.active .profile-modal {
    transform: translateY(0);
}

.avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.current-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 15px;
    border: 3px solid var(--primary-color);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
}

.current-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-upload-label {
    background: linear-gradient(135deg, var(--nav-gradient-start), var(--nav-gradient-end));
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s;
    box-shadow: 0 3px 8px rgba(94, 53, 177, 0.2);
}

.avatar-upload-label:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(94, 53, 177, 0.3);
}

.user-id-section {
    background-color: rgba(94, 53, 177, 0.05);
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
    border-left: 3px solid var(--primary-color);
}

.user-id-section p {
    margin: 0;
    color: #555;
}

.user-id-section #userUniqueId {
    font-weight: bold;
    color: var(--primary-color);
    letter-spacing: 1px;
}

.id-note {
    font-size: 0.8rem;
    color: #777;
    margin-top: 5px !important;
}

/* 头像预览效果 */
.avatar-preview {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.current-avatar:hover .avatar-preview {
    opacity: 1;
}

.avatar-preview-icon {
    color: white;
    font-size: 2rem;
}

/* 个人信息页面按钮样式 */
.profile-buttons {
    display: flex;
    gap: 15px;
    margin-top: 25px;
}

.submit-btn, .logout-btn {
    flex: 1;
}

.logout-btn {
    background: linear-gradient(135deg, #f44336, #e53935);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 30px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 4px 10px rgba(229, 57, 53, 0.2);
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(229, 57, 53, 0.3);
}

/* 页脚样式 */
footer {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-info h3, .footer-links h3, .footer-social h3 {
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.footer-info p {
    margin-bottom: 10px;
}

.footer-links ul {
    list-style: none;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: var(--accent-color);
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icon img {
    width: 30px;
    height: 30px;
    transition: transform 0.3s;
}

.social-icon {
    /* 社交图标样式 */
    display: inline-block;
}
