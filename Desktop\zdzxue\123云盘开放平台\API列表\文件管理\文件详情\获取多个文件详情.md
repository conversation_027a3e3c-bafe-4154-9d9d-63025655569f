# 获取多个文件详情

API：POST 域名 + /api/v1/file/infos

## <font style="color:rgb(38, 38, 38);">Header 参数</font>
| **<font style="color:rgb(38, 38, 38);">名称</font>** | **<font style="color:rgb(38, 38, 38);">类型</font>** | **<font style="color:rgb(38, 38, 38);">是否必填</font>** | **<font style="color:rgb(38, 38, 38);">说明</font>** |
| :---: | :---: | :---: | :---: |
| <font style="color:rgb(38, 38, 38);">Authorization</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(0, 0, 0);">必填</font><font style="color:rgb(38, 38, 38);"></font> | <font style="color:rgb(38, 38, 38);">鉴权access_token</font> |
| <font style="color:rgb(38, 38, 38);">Platform</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(38, 38, 38);">必填</font> | <font style="color:rgb(38, 38, 38);">固定为:open_platform</font> |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| fileIds | []number | 是 | 文件id |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| --- | :---: | :---: | --- |
| fileList | array | 是 |  |
| fileList[*].fileId | number | 是 | 文件ID |
| fileList[*].filename | string | 是 | 文件名 |
| fileList[*].parentFileId | number | 是 | 目录ID |
| fileList[*].type | number | 是 | 0-文件  1-文件夹 |
| fileList[*].etag | string | 是 | md5 |
| fileList[*].size | number | 是 | 文件大小 |
| fileList[*].category | number | 是 | 文件分类：0-未知 1-音频 2-视频 3-图片 |
| fileList[*].status | number | 是 | 文件审核状态。 大于 100 为审核驳回文件 |
| fileList[*].punishFlag | number | 是 | 惩罚标记 |
| fileList[*].s3KeyFlag | string | 是 | 关联s3_key的初始用户标识 |
| fileList[*].storageNode | string | 是 | m0是ceph，m1以上为minio |
| fileList[*].trashed | number | 是 | 是否在回收站：[0：否，1：是] |
| fileList[*].createAt | string | 是 | 创建时间 |
| fileList[*].updateAt | number | 是 | 更新时间 |


## 示例
请求示例

```shell
curl --location 'https://open-api.123pan.com/api/v1/file/infos' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "fileIds": [
        144851864,
        147053066
    ]
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"fileIds\": [\n        144851864,\n        147053066\n    ]\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/file/infos")
.method("POST", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/file/infos",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "fileIds": [
      144851864,
      147053066
    ]
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "fileIds": [
    144851864,
    147053066
  ]
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/file/infos',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
  .then((response) => {
    console.log(JSON.stringify(response.data));
  })
  .catch((error) => {
    console.log(error);
  });

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
    "fileIds": [
        144851864,
        147053066
    ]
})
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("POST", "/api/v1/file/infos", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

响应示例

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "list": [
      {
        "fileId": 144851864,
        "filename": "work",
        "parentFileId": 0,
        "type": 1,
        "etag": "",
        "size": 0,
        "category": 0,
        "status": 0,
        "punishFlag": 0,
        "s3KeyFlag": "1814435920-0",
        "storageNode": "m0",
        "trashed": 0,
        "createAt": "2024-11-08 16:33:50",
        "updateAt": "2024-11-08 16:33:50"
      }
    ]
  },
  "x-traceID": ""
}
```



> 更新: 2025-03-17 19:17:22  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/cqqayfuxybegrlru>