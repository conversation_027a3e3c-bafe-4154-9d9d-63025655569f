<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校评页面调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #6c5ce7;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #5a4fcf;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>校评页面功能测试</h1>
        
        <div class="test-section">
            <h3>1. 登录状态同步测试</h3>
            <p>测试登录状态是否在页面间正确同步</p>
            <button class="test-button" onclick="testLoginStatus()">检查登录状态</button>
            <button class="test-button" onclick="simulateLogin()">模拟登录</button>
            <button class="test-button" onclick="simulateLogout()">模拟退出</button>
            <div id="loginStatus" class="status info">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h3>2. DOM元素检测</h3>
            <p>检查关键DOM元素是否正确获取</p>
            <button class="test-button" onclick="testDOMElements()">检测DOM元素</button>
            <div id="domStatus" class="status info">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h3>3. 事件绑定测试</h3>
            <p>测试按钮点击事件是否正确绑定</p>
            <button class="test-button" onclick="testEventBinding()">测试事件绑定</button>
            <div id="eventStatus" class="status info">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h3>4. 跳转到实际页面</h3>
            <p>在新窗口打开校评页面进行实际测试</p>
            <button class="test-button" onclick="openSchoolReview()">打开校评页面</button>
            <button class="test-button" onclick="openHomePage()">打开主页</button>
        </div>
        
        <div class="test-section">
            <h3>5. 控制台日志</h3>
            <p>打开浏览器开发者工具查看控制台输出</p>
            <div class="info">
                <strong>预期看到的日志：</strong><br>
                - "开始获取DOM元素..."<br>
                - "关键元素获取结果:"<br>
                - 各个DOM元素的获取状态<br>
                - "校评页面初始化完成"
            </div>
        </div>
    </div>

    <script>
        // 测试登录状态
        function testLoginStatus() {
            const statusDiv = document.getElementById('loginStatus');
            
            try {
                const storedUser = localStorage.getItem('currentUser');
                if (storedUser) {
                    const user = JSON.parse(storedUser);
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `已登录用户: ${user.username} (${user.uid})`;
                } else {
                    statusDiv.className = 'status info';
                    statusDiv.innerHTML = '未登录状态';
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '读取登录状态失败: ' + error.message;
            }
        }
        
        // 模拟登录
        function simulateLogin() {
            const testUser = {
                username: 'admin',
                password: 'admin123',
                uid: 'UID_123456',
                avatar: '../images/avatar.png'
            };
            
            localStorage.setItem('currentUser', JSON.stringify(testUser));
            
            // 触发登录事件
            window.dispatchEvent(new CustomEvent('userLoggedIn', {
                detail: { user: testUser }
            }));
            
            const statusDiv = document.getElementById('loginStatus');
            statusDiv.className = 'status success';
            statusDiv.innerHTML = '模拟登录成功！用户: ' + testUser.username;
        }
        
        // 模拟退出
        function simulateLogout() {
            localStorage.removeItem('currentUser');
            
            // 触发退出事件
            window.dispatchEvent(new CustomEvent('userLoggedOut'));
            
            const statusDiv = document.getElementById('loginStatus');
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '已退出登录';
        }
        
        // 测试DOM元素
        function testDOMElements() {
            const statusDiv = document.getElementById('domStatus');
            const elements = [
                'loginBtn',
                'addCommentBox', 
                'loginModalOverlay',
                'commentModalOverlay',
                'searchInput',
                'commentsContainer'
            ];
            
            let results = [];
            elements.forEach(id => {
                const element = document.getElementById(id);
                results.push(`${id}: ${element ? '✓ 找到' : '✗ 未找到'}`);
            });
            
            statusDiv.className = 'status info';
            statusDiv.innerHTML = results.join('<br>');
        }
        
        // 测试事件绑定
        function testEventBinding() {
            const statusDiv = document.getElementById('eventStatus');
            
            // 检查是否有全局的schoolReviewApp实例
            if (typeof schoolReviewApp !== 'undefined') {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = 'schoolReviewApp实例已创建，事件应该已绑定';
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = 'schoolReviewApp实例未找到，可能初始化失败';
            }
        }
        
        // 打开校评页面
        function openSchoolReview() {
            window.open('index.html', '_blank');
        }
        
        // 打开主页
        function openHomePage() {
            window.open('../index.html', '_blank');
        }
        
        // 页面加载时自动检查登录状态
        window.addEventListener('load', function() {
            testLoginStatus();
        });
        
        // 监听登录状态变化
        window.addEventListener('userLoggedIn', function(e) {
            console.log('检测到登录事件:', e.detail);
            testLoginStatus();
        });
        
        window.addEventListener('userLoggedOut', function() {
            console.log('检测到退出登录事件');
            testLoginStatus();
        });
    </script>
</body>
</html>
