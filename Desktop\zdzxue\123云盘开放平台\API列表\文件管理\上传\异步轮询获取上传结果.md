# 异步轮询获取上传结果

API： POST   域名 + /upload/v1/file/upload_async_result

说明：异步轮询获取上传结果

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| preuploadID | string | 必填 | 预上传ID |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| completed | bool | 必填 | 上传合并是否完成,如果为false,请至少1秒后发起轮询 |
| fileID | number | 必填 | 上传完成返回对应fileID |


## 示例
请求示例

```shell
curl --location 'https://open-api.123pan.com/upload/v1/file/upload_async_result' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "preuploadID": "WvjyUgonimrlBq22FJ9e33be4pi04nxxWefNRwRKIqRMBxZgRxyNJ...(过长省略)"
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"preuploadID\": \"WvjyUgonimrlBq22FJ9e33be4pi04nxxWefNRwRKIqRMBxZgRxyNJ...(过长省略)\"\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/upload/v1/file/upload_async_result")
.method("POST", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
const myHeaders = new Headers();
myHeaders.append("Content-Type", "application/json");
myHeaders.append("Platform", "open_platform");
myHeaders.append("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)");

const raw = JSON.stringify({
  "preuploadID": "WvjyUgonimrlBq22FJ9e33be4pi04nxxWefNRwRKIqRMBxZgRxyNJ...(过长省略)"
});

const requestOptions = {
  method: "POST",
  headers: myHeaders,
  body: raw,
  redirect: "follow"
};

fetch("https://open-api.123pan.com/upload/v1/file/upload_async_result", requestOptions)
  .then((response) => response.text())
  .then((result) => console.log(result))
  .catch((error) => console.error(error));
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "preuploadID": "WvjyUgonimrlBq22FJ9e33be4pi04nxxWefNRwRKIqRMBxZgRxyNJ...(过长省略)"
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/upload/v1/file/upload_async_result',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
    "preuploadID": "WvjyUgonimrlBq22FJ9e33be4pi04nxxWefNRwRKIqRMBxZgRxyNJ...(过长省略)"
})
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("POST", "/upload/v1/file/upload_async_result", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

响应示例

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "fileID": 14665463,
    "completed": true
  },
  "x-traceID": "f79fb151-8bb4-47fd-828a-d7d180a76b30_kong-db-5898fdd8c6-t5pvc"
}
```





> 更新: 2025-03-17 19:16:20  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/qgg0sxkfeqygam7e>