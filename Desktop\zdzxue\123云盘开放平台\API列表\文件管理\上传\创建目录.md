# 创建目录

API： POST   域名 + /upload/v1/file/mkdir

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| name | string | 必填 | 目录名(注:不能重名) |
| parentID | number | 必填 | 父目录id，上传到根目录时填写 0 |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| dirID | number | 必填 | 创建的目录ID |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/upload/v1/file/mkdir' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJle...(过长省略)' \
--data '{
    "name": "测试目录名称",
    "parentID": 0
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"parentFileID\": 0,\n    \"filename\": \"测试文件.mp4\",\n    \"etag\": \"4b5c549c4abd0a079caf92d6cad24127\",\n    \"size\": 50650928\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/upload/v1/file/create")
.method("POST", body)
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.addHeader("Content-Type", "application/json")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
   "url": "https://open-api.123pan.com/upload/v1/file/create",
   "method": "POST",
   "timeout": 0,
   "headers": {
      "Platform": "open_platform",
      "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)",
      "Content-Type": "application/json"
   },
   "data": JSON.stringify({
      "parentFileID": 0,
      "filename": "测试文件.mp4",
      "etag": "4b5c549c4abd0a079caf92d6cad24127",
      "size": 50650928
   }),
};

$.ajax(settings).done(function (response) {
   console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "name": "测试目录名称",
  "parentID": 0
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/upload/v1/file/mkdir',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJle...(过长省略)'
  },
  data : data
};

axios.request(config)
  .then((response) => {
    console.log(JSON.stringify(response.data));
  })
  .catch((error) => {
    console.log(error);
  });

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
   "parentFileID": 0,
   "filename": "测试文件.mp4",
   "etag": "4b5c549c4abd0a079caf92d6cad24127",
   "size": 50650928
})
headers = {
   'Platform': 'open_platform',
   'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)',
   'Content-Type': 'application/json'
}
conn.request("POST", "/upload/v1/file/create", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "dirID": 14663228
  },
  "x-traceID": "f2956737-e3a7-4d18-b165-10c347ca6a5e_kong-db-5898fdd8c6-t5pvc"
}
```







> 更新: 2025-03-17 19:16:19  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/gvz09ibuuo97i5ue>